@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 80%;
  background: #fafafa;
  font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Mejoras de accesibilidad y UX */
@layer base {
  * {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white;
  }

  button, a {
    @apply transition-all duration-200 ease-in-out;
  }
}

/* Animaciones sutiles */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.4s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(15px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar corporativo */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-neutral-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-neutral-400 rounded-sm;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-500;
}
