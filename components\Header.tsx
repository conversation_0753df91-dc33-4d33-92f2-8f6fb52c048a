import React from "react";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import LinkCompanyPages from "./LinkCompanyPages";
import Link from "next/link";
import Image from "next/image";
import ColectivoBlanco from "../assets/logo/colectivo_blanco.png";



export default function Header() {
  return (
    <header className="w-full fixed top-0 z-50">
      <Disclosure as="nav" className="bg-primary-700 shadow-2xl border-b border-primary-600">
        <div className="mx-auto max-w-7xl px-4 md:px-6 lg:px-8">
          <div className="relative flex h-18 items-center justify-between">
            {/* Logo - siempre visible */}
            <div className="flex flex-shrink-0 items-center">
              <Link href={"/"} className="flex items-center group">
                <Image
                  src={ColectivoBlanco}
                  alt="Colectivos Norte Jujuy"
                  className="h-8 w-8 group-hover:scale-105 transition-all duration-300"
                  width={32}
                  height={32}
                />
                <div className="flex flex-col ml-3">
                  <p className="hidden md:block text-lg font-bold text-white tracking-wide">
                    Colectivos al Norte
                  </p>
                  <div className="hidden md:block w-full h-0.5 bg-gradient-to-r from-white/80 via-white/40 to-transparent mt-1"></div>
                </div>
              </Link>
            </div>

            {/* Título centrado en móvil */}
            <div className="flex flex-col items-center md:hidden">
              <p className="font-bold text-white text-base tracking-wide">
                Colectivos al Norte
              </p>
              <div className="w-20 h-0.5 bg-gradient-to-r from-white/80 via-white/40 to-white/80 mt-1"></div>
            </div>

            {/* Información de origen */}
            <div className="hidden md:flex md:ml-6 items-center">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-white/60 rounded-full"></div>
                <p className="text-white/90 text-sm font-medium tracking-wide">
                  Desde San Salvador de Jujuy
                </p>
              </div>
            </div>

            {/* Botón menú móvil */}
            <div className="flex items-center md:hidden">
              <DisclosureButton className="group relative inline-flex items-center justify-center rounded-lg p-2.5 text-white hover:bg-white/15 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/40 transition-all duration-300 border border-white/20 backdrop-blur-sm shadow-lg hover:shadow-xl">
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Open main menu</span>
                <Bars3Icon
                  aria-hidden="true"
                  className="block h-5 w-5 group-data-[open]:hidden"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-5 w-5 group-data-[open]:block"
                />
              </DisclosureButton>
            </div>
          </div>
        </div>

        <DisclosurePanel className="md:hidden">
          <div className="px-4 pb-4 pt-2 bg-primary-800 border-t border-primary-600">
            <div className="flex items-center justify-center gap-2 py-3">
              <div className="w-2 h-2 bg-white/60 rounded-full"></div>
              <p className="text-white/90 text-sm font-medium tracking-wide">
                Desde San Salvador de Jujuy
              </p>
            </div>
          </div>
        </DisclosurePanel>
      </Disclosure>
      <LinkCompanyPages />
    </header>
  );
}
