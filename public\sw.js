const CACHE_NAME = 'colectivos-norte-jujuy-v1';
const STATIC_CACHE_NAME = 'colectivos-static-v1';
const DYNAMIC_CACHE_NAME = 'colectivos-dynamic-v1';

// Recursos estáticos para cachear
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png',
  '/apple-touch-icon.png',
  '/favicon.png'
];

// Recursos dinámicos que queremos cachear
const CACHE_STRATEGIES = {
  // Páginas principales
  pages: [
    '/',
    '/evelia',
    '/santa-ana',
    '/panamericano',
    '/el-quiaqueno',
    '/vientos-del-norte',
    '/jamabus',
    '/balut'
  ],
  // APIs y datos
  api: [
    '/api/schedules',
    '/api/companies'
  ]
};

// Instalación del Service Worker
self.addEventListener('install', (event) => {
  console.log('🔧 Service Worker: Instalando...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('📦 Service Worker: Cacheando recursos estáticos');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('✅ Service Worker: Instalación completada');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('❌ Service Worker: Error en instalación:', error);
      })
  );
});

// Activación del Service Worker
self.addEventListener('activate', (event) => {
  console.log('🚀 Service Worker: Activando...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            // Eliminar caches antiguos
            if (cacheName !== STATIC_CACHE_NAME && 
                cacheName !== DYNAMIC_CACHE_NAME &&
                cacheName.startsWith('colectivos-')) {
              console.log('🗑️ Service Worker: Eliminando cache antiguo:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker: Activación completada');
        return self.clients.claim();
      })
  );
});

// Interceptar requests (fetch)
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Solo manejar requests del mismo origen
  if (url.origin !== location.origin) {
    return;
  }

  event.respondWith(
    handleRequest(request)
  );
});

// Estrategia de manejo de requests
async function handleRequest(request) {
  const url = new URL(request.url);
  
  try {
    // Estrategia Cache First para recursos estáticos
    if (isStaticAsset(url.pathname)) {
      return await cacheFirst(request);
    }
    
    // Estrategia Network First para páginas y APIs
    if (isPageOrAPI(url.pathname)) {
      return await networkFirst(request);
    }
    
    // Estrategia por defecto: Network First
    return await networkFirst(request);
    
  } catch (error) {
    console.error('❌ Service Worker: Error manejando request:', error);
    
    // Fallback para páginas
    if (request.mode === 'navigate') {
      return await caches.match('/') || new Response('Offline - Colectivos Norte Jujuy no disponible');
    }
    
    return new Response('Recurso no disponible offline', { status: 404 });
  }
}

// Cache First: Buscar en cache primero, luego red
async function cacheFirst(request) {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  const networkResponse = await fetch(request);
  
  if (networkResponse.ok) {
    const cache = await caches.open(STATIC_CACHE_NAME);
    cache.put(request, networkResponse.clone());
  }
  
  return networkResponse;
}

// Network First: Intentar red primero, luego cache
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
}

// Verificar si es un recurso estático
function isStaticAsset(pathname) {
  return pathname.startsWith('/icons/') ||
         pathname.startsWith('/_next/static/') ||
         pathname.endsWith('.png') ||
         pathname.endsWith('.jpg') ||
         pathname.endsWith('.jpeg') ||
         pathname.endsWith('.svg') ||
         pathname.endsWith('.ico') ||
         pathname === '/manifest.json';
}

// Verificar si es una página o API
function isPageOrAPI(pathname) {
  return CACHE_STRATEGIES.pages.includes(pathname) ||
         pathname.startsWith('/api/') ||
         pathname === '/' ||
         !pathname.includes('.');
}

// Manejo de mensajes del cliente
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data && event.data.type === 'GET_VERSION') {
    event.ports[0].postMessage({ version: CACHE_NAME });
  }
});

console.log('🚌 Service Worker: Colectivos Norte Jujuy PWA cargado');
