const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Iconos esperados
const expectedIcons = [
  { file: 'icon-72x72.png', size: 72 },
  { file: 'icon-96x96.png', size: 96 },
  { file: 'icon-128x128.png', size: 128 },
  { file: 'icon-144x144.png', size: 144 },
  { file: 'icon-152x152.png', size: 152 },
  { file: 'icon-192x192.png', size: 192 },
  { file: 'icon-384x384.png', size: 384 },
  { file: 'icon-512x512.png', size: 512 },
];

const specialIcons = [
  { file: 'favicon.png', expectedSize: 32 },
  { file: 'apple-touch-icon.png', expectedSize: 180 },
];

async function verifyIcons() {
  console.log('🔍 Verificando iconos PWA generados...\n');
  
  let allValid = true;
  
  // Verificar iconos principales
  console.log('📱 Iconos PWA principales:');
  for (const icon of expectedIcons) {
    const iconPath = path.join(__dirname, '../public/icons', icon.file);
    
    if (!fs.existsSync(iconPath)) {
      console.log(`❌ ${icon.file} - NO ENCONTRADO`);
      allValid = false;
      continue;
    }
    
    try {
      const metadata = await sharp(iconPath).metadata();
      const sizeMatch = metadata.width === icon.size && metadata.height === icon.size;
      const status = sizeMatch ? '✅' : '⚠️';
      
      console.log(`${status} ${icon.file} - ${metadata.width}x${metadata.height} (esperado: ${icon.size}x${icon.size})`);
      
      if (!sizeMatch) {
        allValid = false;
      }
    } catch (error) {
      console.log(`❌ ${icon.file} - ERROR: ${error.message}`);
      allValid = false;
    }
  }
  
  // Verificar iconos especiales
  console.log('\n🍎 Iconos especiales:');
  for (const icon of specialIcons) {
    const iconPath = path.join(__dirname, '../public', icon.file);
    
    if (!fs.existsSync(iconPath)) {
      console.log(`❌ ${icon.file} - NO ENCONTRADO`);
      allValid = false;
      continue;
    }
    
    try {
      const metadata = await sharp(iconPath).metadata();
      const sizeMatch = metadata.width === icon.expectedSize && metadata.height === icon.expectedSize;
      const status = sizeMatch ? '✅' : '⚠️';
      
      console.log(`${status} ${icon.file} - ${metadata.width}x${metadata.height} (esperado: ${icon.expectedSize}x${icon.expectedSize})`);
      
      if (!sizeMatch) {
        allValid = false;
      }
    } catch (error) {
      console.log(`❌ ${icon.file} - ERROR: ${error.message}`);
      allValid = false;
    }
  }
  
  // Verificar Service Worker
  console.log('\n⚙️ Archivos PWA:');
  const swPath = path.join(__dirname, '../public/sw.js');
  const swExists = fs.existsSync(swPath);
  console.log(`${swExists ? '✅' : '❌'} sw.js - ${swExists ? 'ENCONTRADO' : 'NO ENCONTRADO'}`);
  
  // Verificar Manifest
  const manifestPath = path.join(__dirname, '../app/manifest.ts');
  const manifestExists = fs.existsSync(manifestPath);
  console.log(`${manifestExists ? '✅' : '❌'} manifest.ts - ${manifestExists ? 'ENCONTRADO' : 'NO ENCONTRADO'}`);
  
  // Resumen final
  console.log('\n📊 RESUMEN:');
  if (allValid && swExists && manifestExists) {
    console.log('🎉 ¡Todos los iconos PWA están correctos y listos!');
    console.log('✅ La aplicación está completamente configurada como PWA');
    console.log('\n🚀 Para probar:');
    console.log('   1. Ejecuta: npm run dev -- --experimental-https');
    console.log('   2. Abre la app en móvil');
    console.log('   3. Busca el prompt de "Instalar app"');
  } else {
    console.log('⚠️ Algunos iconos o archivos PWA tienen problemas');
    console.log('🔧 Ejecuta el script de generación nuevamente si es necesario');
    allValid = false;
  }
  
  return allValid;
}

verifyIcons().catch(console.error);
