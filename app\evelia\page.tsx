import React from "react";
import { mockBusSchedules } from "@/mocks/busSchedules";
import CardScheduleBus from "@/components/CardScheduleBus";
import { Metadata } from "next";
import TitlePages from "@/components/TitlePages";

export const metadata: Metadata = {
  title: "Evelia SA Horarios - Norte de Jujuy",
  description:
    "Horarios del colectivo Evelia, viajes a Volcan, Purmamarca, Tilcara, Humahuaca, la Quiaca. Consulta los horarios de los colectivos al norte de Jujuy",
  keywords: [
    "Evelia SA",
    "colectivos al norte",
    "colectivos a Tilcara",
    "colectivos a Humahuaca",
    "colectivos a la Quiaca",
    "horario colectivos",
  ],
  robots: {
    index: true,
    follow: true,
  },
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
};

export default function page() {
  const eveliaFilter = mockBusSchedules.filter(
    (bus) => bus.nameCompany === "Evelia S.A",
  );
  return (
    <div>
      <TitlePages title={"Horarios del Evelia al Norte"} />
      <CardScheduleBus mockBusSchedules={eveliaFilter} />
    </div>
  );
}
