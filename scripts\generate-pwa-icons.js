const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Tamaños de iconos necesarios para PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Rutas
const inputPath = path.join(__dirname, '../assets/logo/colectivo_blanco.png');
const outputDir = path.join(__dirname, '../public/icons');

// Crear directorio si no existe
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

async function generateIcons() {
  console.log('🎨 Generando iconos PWA...');
  
  try {
    // Verificar que el archivo fuente existe
    if (!fs.existsSync(inputPath)) {
      throw new Error(`Archivo fuente no encontrado: ${inputPath}`);
    }

    // Generar cada tamaño de icono
    for (const size of iconSizes) {
      const outputPath = path.join(outputDir, `icon-${size}x${size}.png`);
      
      await sharp(inputPath)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 6, g: 54, b: 124, alpha: 1 } // Color primario #06367c
        })
        .png()
        .toFile(outputPath);
      
      console.log(`✅ Generado: icon-${size}x${size}.png`);
    }

    // Generar favicon.ico
    const faviconPath = path.join(__dirname, '../public/favicon.ico');
    await sharp(inputPath)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 6, g: 54, b: 124, alpha: 1 }
      })
      .png()
      .toFile(faviconPath.replace('.ico', '.png'));
    
    console.log('✅ Generado: favicon.png');

    // Generar apple-touch-icon
    const appleTouchIconPath = path.join(__dirname, '../public/apple-touch-icon.png');
    await sharp(inputPath)
      .resize(180, 180, {
        fit: 'contain',
        background: { r: 6, g: 54, b: 124, alpha: 1 }
      })
      .png()
      .toFile(appleTouchIconPath);
    
    console.log('✅ Generado: apple-touch-icon.png');

    console.log('🎉 ¡Todos los iconos PWA generados exitosamente!');
    
  } catch (error) {
    console.error('❌ Error generando iconos:', error.message);
    process.exit(1);
  }
}

generateIcons();
