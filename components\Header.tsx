import React from "react";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import LinkCompanyPages from "./LinkCompanyPages";
import Link from "next/link";
import Image from "next/image";
import ColectivoBlanco from "../assets/logo/colectivo_blanco.png";

const navigation = [
  { name: "Desde San Salvador", href: "/", current: false },
  // { name: "Desde Humahuaca", href: "#", current: false },
  // { name: "Desde Purmamarca", href: "#", current: false },
  // { name: "Desde la Quiaca", href: "#", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function Header() {
  return (
    <header className="w-full fixed top-0 z-50">
      <Disclosure as="nav" className="bg-gradient-hero shadow-2xl backdrop-blur-md border-b border-white/10">
        <div className="mx-auto max-w-7xl px-4 md:px-6 lg:px-8">
          <div className="relative flex h-18 items-center justify-between">
            {/* Logo - siempre visible */}
            <div className="flex flex-shrink-0 items-center">
              <Link href={"/"} className="flex items-center group">
                <div className="relative">
                  <div className="absolute inset-0 bg-white/20 rounded-xl blur-sm group-hover:bg-white/30 transition-all duration-300"></div>
                  <Image
                    src={ColectivoBlanco}
                    alt="Colectivos Norte Jujuy"
                    className="relative h-12 w-12 group-hover:scale-110 transition-all duration-300 drop-shadow-lg"
                    width={48}
                    height={48}
                  />
                </div>
                <div className="flex flex-col ml-4">
                  <p className="hidden md:block text-xl font-bold text-white tracking-wide">
                    Colectivos al Norte
                  </p>
                  <div className="hidden md:block flex items-center gap-1 mt-1">
                    <div className="w-2 h-2 bg-white/80 rounded-full"></div>
                    <div className="flex-1 h-0.5 bg-gradient-to-r from-white/60 to-transparent"></div>
                  </div>
                </div>
              </Link>
            </div>

            {/* Título centrado en móvil */}
            <div className="flex flex-col items-center md:hidden">
              <p className="font-bold text-white text-base tracking-wide">
                Colectivos al Norte
              </p>
              <div className="flex items-center gap-1 mt-1">
                <div className="w-1.5 h-1.5 bg-white/80 rounded-full"></div>
                <div className="w-12 h-0.5 bg-gradient-to-r from-white/60 via-white/80 to-white/60"></div>
                <div className="w-1.5 h-1.5 bg-white/80 rounded-full"></div>
              </div>
            </div>

            {/* Navegación desktop */}
            <div className="hidden md:flex md:ml-6">
              <div className="flex space-x-2">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    aria-current={item.current ? "page" : undefined}
                    className={classNames(
                      item.current
                        ? "bg-white/20 text-white border border-white/30 shadow-lg"
                        : "text-white/90 hover:bg-white/15 hover:text-white border border-white/20 hover:border-white/40 hover:shadow-md",
                      "rounded-lg px-4 py-2.5 text-sm font-semibold tracking-wide transition-all duration-300 backdrop-blur-sm",
                    )}
                  >
                    {item.name}
                  </Link>
                ))}
              </div>
            </div>

            {/* Botón menú móvil */}
            <div className="flex items-center md:hidden">
              <DisclosureButton className="group relative inline-flex items-center justify-center rounded-lg p-2.5 text-white hover:bg-white/15 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/40 transition-all duration-300 border border-white/20 backdrop-blur-sm shadow-lg hover:shadow-xl">
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Open main menu</span>
                <Bars3Icon
                  aria-hidden="true"
                  className="block h-5 w-5 group-data-[open]:hidden"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-5 w-5 group-data-[open]:block"
                />
              </DisclosureButton>
            </div>
          </div>
        </div>

        <DisclosurePanel className="md:hidden">
          <div className="space-y-1 px-4 pb-4 pt-2 bg-primary-800/95 border-t border-white/10">
            {navigation.map((item) => (
              <DisclosureButton
                key={item.name}
                as="a"
                href={item.href}
                aria-current={item.current ? "page" : undefined}
                className={classNames(
                  item.current
                    ? "bg-white/15 text-white border border-white/20"
                    : "text-white/85 hover:bg-white/8 hover:text-white border border-transparent",
                  "block rounded-md px-4 py-2.5 text-base font-medium transition-all duration-200",
                )}
              >
                {item.name}
              </DisclosureButton>
            ))}
          </div>
        </DisclosurePanel>
      </Disclosure>
      <LinkCompanyPages />
    </header>
  );
}
