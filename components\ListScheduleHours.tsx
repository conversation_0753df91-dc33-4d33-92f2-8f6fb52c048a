import React from "react";

interface IListScheduleHoursProps {
  company: string[] | undefined;
}

export default function ListScheduleHours({
  company,
}: IListScheduleHoursProps) {
  return (
    <ul className="flex flex-wrap gap-2">
      {company?.map((hour, i) => {
        return (
          <li
            key={i}
            className="px-3 py-2 bg-white border border-neutral-200 hover:border-primary-300 rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 hover:scale-105 cursor-default group"
          >
            <p className="text-neutral-800 font-semibold text-sm group-hover:text-primary-700 transition-colors duration-300">
              {hour}
            </p>
          </li>
        );
      })}
    </ul>
  );
}
