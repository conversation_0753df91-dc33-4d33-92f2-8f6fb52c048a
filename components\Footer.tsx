import React from "react";
import Image from "next/image";
import ColectivoBlanco from "../assets/logo/colectivo_blanco.png";

export default function Footer() {
  return (
    <footer className="bg-primary-700 text-white py-8 mt-12 border-t border-primary-600">
      <div className="container mx-auto px-4">
        <div className="flex flex-col space-y-6 md:flex-row md:justify-between md:items-center md:space-y-0">
          {/* Logo y título */}
          <div className="text-center md:text-left">
            <div className="flex items-center justify-center md:justify-start gap-3 mb-3">
              <Image
                src={ColectivoBlanco}
                alt="Colectivos Norte Jujuy"
                className="h-8 w-8"
                width={32}
                height={32}
              />
              <h3 className="text-lg font-bold text-white">Colectivos Norte Jujuy</h3>
            </div>
            <p className="text-primary-100 text-sm max-w-md mx-auto md:mx-0">
              Información actualizada de horarios de transporte público hacia el norte de Jujuy.
            </p>
          </div>

          {/* Avisos importantes */}
          <div className="text-center">
            <p className="text-primary-200 text-xs mb-1">
              Horarios sujetos a cambios sin previo aviso
            </p>
            <p className="text-primary-200 text-xs">
              Confirme con las empresas antes de viajar
            </p>
          </div>

          {/* Copyright */}
          <div className="text-center md:text-right">
            <p className="text-sm text-primary-200 mb-2">
              &copy; {new Date().getFullYear()} Todos los derechos reservados.
            </p>
            <div className="w-16 h-0.5 bg-gradient-to-r from-white/60 via-white/30 to-white/60 mx-auto md:mx-0 md:ml-auto"></div>
          </div>
        </div>
      </div>
    </footer>
  );
}
