import React from "react";
import { mockBusSchedules } from "@/mocks/busSchedules";
import CardScheduleBus from "@/components/CardScheduleBus";
import { Metadata } from "next";
import TitlePages from "@/components/TitlePages";

export const metadata: Metadata = {
  title: "El Quiaqueño Horarios - Norte de Jujuy",
  description:
    "Horarios del colectivo El Quiaqueño, viajes a Volcan, Purmamarca, Tilcara, Humahuaca, la Quiaca. Consulta los horarios de los colectivos al norte de Jujuy",
  keywords: [
    "El quiaqueño",
    "colectivos al norte",
    "colectivos a Tilcara",
    "colectivos a Humahuaca",
    "colectivos a la Quiaca",
    "horario colectivos",
  ],
  robots: {
    index: true,
    follow: true,
  },
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
};

export default function page() {
  const elQuiaquenioFilter = mockBusSchedules.filter(
    (bus) => bus.nameCompany === "El Quiaqueño SRL",
  );
  return (
    <div>
      <TitlePages title={"Horarios de El Quiaqueño al Norte"} />
      <CardScheduleBus mockBusSchedules={elQuiaquenioFilter} />
    </div>
  );
}
