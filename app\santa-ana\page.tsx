import React from "react";
import { mockBusSchedules } from "@/mocks/busSchedules";
import CardScheduleBus from "@/components/CardScheduleBus";
import { Metadata } from "next";
import TitlePages from "@/components/TitlePages";

export const metadata: Metadata = {
  title: "Santa Ana Horarios - Norte de Jujuy",
  description:
    "Horarios del colectivo Santa Ana, viajes a Barcena, Volcan, Purmamarca, Tilcara, Humahuaca. Consulta los horarios de los colectivos al norte de Jujuy",
  keywords: [
    "Santa Ana Jujuy",
    "colectivos al norte",
    "colectivos a Tilcara",
    "colectivos a Humahuaca",
    "colectivos a Purmamarca",
    "horario colectivos",
  ],
  robots: {
    index: true,
    follow: true,
  },
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
};

export default function page() {
  const santaAnaFilter = mockBusSchedules.filter(
    (schedule) => schedule.nameCompany === "Santa Ana",
  );
  return (
    <div>
      <TitlePages title={"Horarios de Santa Ana al Norte"} />
      <CardScheduleBus mockBusSchedules={santaAnaFilter} />
    </div>
  );
}
