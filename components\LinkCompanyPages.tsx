import React from "react";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { FunnelIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

const navigation = [
  { name: "Todos", href: "/", current: false },
  { name: "<PERSON><PERSON>", href: "/evelia", current: false },
  { name: "Santa Ana", href: "/santa-ana", current: false },
  { name: "Panamericano", href: "/panamericano", current: false },
  { name: "El Quiaqueño", href: "/el-quiaquenio", current: false },
  { name: "Vientos del Norte", href: "/vientos-del-norte", current: false },
  { name: "Jamabus", href: "/jamabus", current: false },
  { name: "Balut", href: "/balut", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function LinkCompanyPages() {
  return (
    <div className="w-full">
      <Disclosure as="nav" className="bg-gradient-corporate border-b border-neutral-300 shadow-corporate">
        <div className="mx-auto max-w-7xl">
          <div className="relative flex items-center sm:justify-between">
            <div className="flex sm:w-full items-center justify-between sm:items-stretch">
              <div className="hidden w-full sm:block">
                <div className="flex w-full justify-between gap-px p-1">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      aria-current={item.current ? "page" : undefined}
                      className={classNames(
                        item.current
                          ? "bg-primary-700 text-white shadow-corporate"
                          : "text-neutral-700 hover:bg-primary-50 hover:text-primary-700 border border-transparent hover:border-primary-200",
                        "px-3 py-2 w-full text-center text-sm font-medium rounded-md transition-all duration-200",
                      )}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <div className="inset-y-0 flex items-center sm:hidden">
              {/* Mobile filter button - más compacto y moderno */}
              <DisclosureButton className="group relative inline-flex items-center justify-center bg-white/10 backdrop-blur-sm rounded-full px-3 py-1.5 text-white hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white/30 transition-all duration-200 border border-white/20">
                <FunnelIcon
                  aria-hidden="true"
                  className="block h-4 w-4 group-data-[open]:hidden mr-1.5"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-4 w-4 group-data-[open]:block mr-1.5"
                />
                <span className="text-xs font-medium">
                  Filtros
                </span>
                <div className="ml-1.5 w-1 h-1 bg-white/60 rounded-full group-data-[open]:rotate-180 transition-transform duration-200"></div>
              </DisclosureButton>
            </div>
          </div>
        </div>

        <DisclosurePanel className="sm:hidden">
          <div className="mx-4 mt-2 mb-4 bg-gradient-hero rounded-xl shadow-xl border border-primary-800 overflow-hidden">
            <div className="px-4 py-3 border-b border-primary-600">
              <h3 className="text-sm font-semibold text-white flex items-center gap-2">
                <FunnelIcon className="h-4 w-4 text-white" />
                Filtrar por empresa
              </h3>
            </div>
            <div className="p-3 space-y-2">
              {navigation.map((item) => (
                <DisclosureButton
                  key={item.name}
                  as="a"
                  href={item.href}
                  aria-current={item.current ? "page" : undefined}
                  className={classNames(
                    item.current
                      ? "bg-white/20 text-white shadow-md border border-white/30"
                      : "text-white/90 hover:bg-white/10 hover:text-white border border-white/20 hover:border-white/40 hover:shadow-sm",
                    "block rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200",
                  )}
                >
                  {item.name}
                </DisclosureButton>
              ))}
            </div>
          </div>
        </DisclosurePanel>
      </Disclosure>
    </div>
  );
}
