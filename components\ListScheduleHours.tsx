import React from "react";

interface IListScheduleHoursProps {
  company: string[] | undefined;
}

export default function ListScheduleHours({
  company,
}: IListScheduleHoursProps) {
  return (
    <ul className="flex flex-wrap">
      {company?.map((hour, i) => {
        return (
          <li key={i} className="mx-1 mb-1 p-1 w-[70px] bg-slate-200 rounded">
            <p className="text-gray-900">{hour}</p>
            {""}
          </li>
        );
      })}
    </ul>
  );
}
