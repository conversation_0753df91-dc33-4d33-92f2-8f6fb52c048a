import React from "react";
import { mockBusSchedules } from "@/mocks/busSchedules";
import CardScheduleBus from "@/components/CardScheduleBus";
import { Metadata } from "next";
import TitlePages from "@/components/TitlePages";

export const metadata: Metadata = {
  title: "Jamabus Horarios - Norte de Jujuy",
  description:
    "Horarios del colectivo Jamabus, viajes a la Quiaca. Consulta los horarios de los colectivos al norte de Jujuy",
  keywords: [
    "Jamabus",
    "Horarios Jamabus",
    "colectivos a la Quiaca",
    "horario colectivos",
  ],
  robots: {
    index: true,
    follow: true,
  },
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
};

export default function page() {
  const jamabusFilter = mockBusSchedules.filter(
    (bus) => bus.nameCompany === "JAMABUS",
  );
  return (
    <div>
      <TitlePages title={"Horarios de Jamabus al Norte"} />
      <CardScheduleBus mockBusSchedules={jamabusFilter} />
    </div>
  );
}
