# 🚌 Colectivos Norte Jujuy

Una aplicación web moderna para consultar horarios de colectivos que viajan desde San Salvador de Jujuy hacia el norte de la provincia, incluyendo destinos como Humahuaca, Tilcara, Purmamarca y otras localidades de la Quebrada de Humahuaca.

## 🌟 Características

- **Horarios actualizados** de múltiples empresas de transporte
- **Diseño responsive** optimizado para dispositivos móviles y desktop
- **Información detallada** de cada empresa con logos y datos de contacto
- **Horarios diferenciados** para días de semana, fines de semana y feriados
- **Servicios directos** claramente identificados
- **Galería de imágenes** para algunas rutas
- **SEO optimizado** con sitemap automático

## 🛠️ Tecnologías Utilizadas

### Frontend Framework
- **[Next.js 14.2.5](https://nextjs.org/)** - Framework de React con App Router
- **[React 18](https://reactjs.org/)** - Biblioteca de JavaScript para interfaces de usuario
- **[TypeScript 5](https://www.typescriptlang.org/)** - Superset tipado de JavaScript

### Estilos y UI
- **[Tailwind CSS 3.4.1](https://tailwindcss.com/)** - Framework de CSS utility-first
- **[Headless UI](https://headlessui.com/)** - Componentes de UI sin estilos
- **[Heroicons](https://heroicons.com/)** - Iconos SVG
- **[React Icons](https://react-icons.github.io/react-icons/)** - Biblioteca de iconos

### Utilidades
- **[Moment.js](https://momentjs.com/)** - Manipulación y formateo de fechas
- **[UUID](https://www.npmjs.com/package/uuid)** - Generación de identificadores únicos
- **[Next Sitemap](https://www.npmjs.com/package/next-sitemap)** - Generación automática de sitemap

### Herramientas de Desarrollo
- **[ESLint](https://eslint.org/)** - Linter para JavaScript/TypeScript
- **[Prettier](https://prettier.io/)** - Formateador de código
- **[PostCSS](https://postcss.org/)** - Procesador de CSS

## 📁 Estructura del Proyecto

```
colectivos-norte-jujuy/
├── app/                          # App Router de Next.js
│   ├── balut/                   # Página de empresa Balut
│   ├── el-quiaquenio/          # Página de empresa El Quiaqueño
│   ├── evelia/                 # Página de empresa Evelia
│   ├── jamabus/                # Página de empresa Jamabus
│   ├── panamericano/           # Página de empresa Panamericano
│   ├── santa-ana/              # Página de empresa Santa Ana
│   ├── vientos-del-norte/      # Página de empresa Vientos del Norte
│   ├── globals.css             # Estilos globales
│   ├── layout.tsx              # Layout principal
│   └── page.tsx                # Página de inicio
├── components/                  # Componentes reutilizables
│   ├── AdditionalInformation.tsx
│   ├── AllSchedules.tsx
│   ├── CardScheduleBus.tsx
│   ├── Footer.tsx
│   ├── GalleryOfImages.tsx
│   ├── GoogleAnalytics.tsx
│   ├── Header.tsx
│   ├── LinkCompanyPages.tsx
│   ├── ListScheduleHours.tsx
│   ├── ModalImages.tsx
│   └── TitlePages.tsx
├── models/                      # Definiciones de tipos TypeScript
│   └── busSchedules.model.ts
├── mocks/                       # Datos de prueba
│   └── busSchedules.ts
├── assets/                      # Recursos estáticos
│   ├── company/                # Logos de empresas
│   └── jujuy.png
├── utils/                       # Utilidades y constantes
│   └── constants/
│       └── busSchedule.ts
├── public/                      # Archivos públicos
│   ├── robots.txt
│   └── sitemap.xml
├── package.json                 # Dependencias y scripts
├── tailwind.config.ts          # Configuración de Tailwind
├── tsconfig.json               # Configuración de TypeScript
├── next.config.mjs             # Configuración de Next.js
└── next-sitemap.config.js      # Configuración del sitemap
```

## 🚀 Instalación y Configuración

### Prerrequisitos
- **Node.js 18.18.0** o superior
- npm, yarn o pnpm

### Pasos de instalación

1. **Clonar el repositorio**
   ```bash
   git clone https://github.com/tu-usuario/colectivos-norte-jujuy.git
   cd colectivos-norte-jujuy
   ```

2. **Instalar dependencias**
   ```bash
   npm install
   ```

3. **Ejecutar en modo desarrollo**
   ```bash
   npm run dev
   ```

4. **Abrir en el navegador**
   Visita [http://localhost:3000](http://localhost:3000)

## 📜 Scripts Disponibles

- `npm run dev` - Inicia el servidor de desarrollo
- `npm run build` - Construye la aplicación para producción
- `npm run start` - Inicia el servidor de producción
- `npm run lint` - Ejecuta ESLint para verificar el código
- `npm run format` - Formatea el código con Prettier

## 🏢 Empresas de Transporte Incluidas

La aplicación incluye información de las siguientes empresas:

1. **Evelia S.A** - Servicios a Humahuaca
2. **Santa Ana** - Múltiples destinos del norte
3. **Vientos del Norte** - Servicios regionales
4. **Panamericano** - Rutas de larga distancia
5. **El Quiaqueño** - Servicios locales
6. **Jamabus** - Transporte regional
7. **Balut** - Servicios especializados

## 🗺️ Destinos Cubiertos

- **Humahuaca** - Principal destino turístico
- **Tilcara** - Pueblo histórico de la Quebrada
- **Purmamarca** - Famoso por el Cerro de los Siete Colores
- **Maimará** - Localidad intermedia
- **Volcán** - Pueblo de montaña
- **Tumbaya** - Estación intermedia
- **Y otros destinos del norte jujeño**

## 🔧 Configuración

### Variables de Entorno
El proyecto no requiere variables de entorno específicas para funcionar en desarrollo.

### Configuración de Imágenes
Las imágenes remotas están configuradas para aceptar dominios de:
- `images.unsplash.com`
- `i.pinimg.com`

## 📱 Características Técnicas

- **SSG (Static Site Generation)** - Páginas pre-renderizadas para mejor performance
- **Responsive Design** - Adaptado para móviles, tablets y desktop
- **SEO Optimizado** - Meta tags, sitemap automático y estructura semántica
- **Accesibilidad** - Componentes accesibles con Headless UI
- **Performance** - Optimización de imágenes con Next.js Image
- **TypeScript** - Tipado estático para mayor robustez del código

## 🚀 Despliegue

### Instrucciones para subir cambios

1. **Desarrollo**: Utilizar la branch `develop` para todos los cambios
2. **Testing**: Verificar los cambios en el preview de Vercel
3. **Producción**: Hacer merge a `main` cuando los cambios estén verificados
4. **Deploy automático**: La aplicación se despliega automáticamente en Vercel

### Flujo de trabajo recomendado
```bash
# Crear nueva feature branch desde develop
git checkout develop
git pull origin develop
git checkout -b feature/nueva-funcionalidad

# Hacer cambios y commits
git add .
git commit -m "feat: descripción del cambio"

# Push y crear PR hacia develop
git push origin feature/nueva-funcionalidad
```

## 🤝 Contribuir

1. Fork el proyecto
2. Crea una rama para tu feature desde `develop`
3. Commit tus cambios siguiendo conventional commits
4. Push a la rama y crea un Pull Request hacia `develop`
5. Una vez aprobado y testeado, se hará merge a `main`

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 📞 Contacto

Para consultas sobre horarios o información adicional, contacta directamente con las empresas de transporte correspondientes.

---

**Nota**: Los horarios mostrados en la aplicación son referenciales y pueden estar sujetos a cambios. Se recomienda confirmar con las empresas de transporte antes de viajar.
