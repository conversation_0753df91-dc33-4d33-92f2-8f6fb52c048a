"use client";
import Link from "next/link";
import React, { useState } from "react";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";
import { PhotoIcon } from "@heroicons/react/24/outline";
import ModalImages from "./ModalImages";
import { IImageLink } from "@/models/busSchedules.model";

export default function AdditionalInformation({dataImages} : {dataImages: IImageLink[]}) {
  const [open, setOpen] = useState(false);
  const [openModal, setOpenModal] = useState(false);

  return (
    <>
      {/* Botón de imágenes siempre visible y prominente */}
      <div className="mb-4">
        <button
          onClick={() => setOpenModal(true)}
          className="w-full bg-primary-700 hover:bg-primary-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
        >
          <PhotoIcon className="h-5 w-5" />
          <span>Ver Capturas de Boletería</span>
          <span className="bg-white/20 text-xs px-2 py-1 rounded-full">
            {dataImages?.length || 0} fotos
          </span>
        </button>
      </div>

      {/* Acordeón para información adicional (opcional) */}
      <div className="border-t border-gray-200 pt-4">
        <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
          <dt className="text-sm font-medium leading-6 text-gray-900">
            <div
              onClick={() => setOpen(!open)}
              className="flex items-center text-primary-700 hover:text-primary-800 cursor-pointer transition-colors duration-200"
            >
              <p className="font-medium">Información adicional</p>
              <div className="ml-2 transition-transform duration-200">
                {!open && <IoIosArrowDown className="h-4 w-4" />}
                {open && <IoIosArrowUp className="h-4 w-4" />}
              </div>
            </div>
          </dt>
        </div>

        {open && (
          <div className="mt-3 space-y-3 text-sm text-gray-600">
            <div className="bg-gray-50 p-3 rounded-lg">
              <p className="font-medium text-gray-900 mb-1">Sobre las capturas:</p>
              <p>Las imágenes muestran los horarios oficiales de la terminal de ómnibus.</p>
              <p className="mt-1">Horarios sujetos a cambios sin previo aviso.</p>
            </div>
            <div className="bg-primary-50 p-3 rounded-lg border border-primary-200">
              <p className="font-medium text-primary-900 mb-1">Recomendación:</p>
              <p className="text-primary-800">Confirme siempre los horarios directamente con la empresa antes de viajar.</p>
            </div>
          </div>
        )}
      </div>

      {/* Modal de imágenes */}
      <ModalImages open={openModal} setOpen={setOpenModal} dataImages={dataImages} />
    </>
  );
}
