import React from "react";
import Image from "next/image";
import JujuyImage from "./../assets/jujuy.png";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import LinkCompanyPages from "./LinkCompanyPages";
import Link from "next/link";

const navigation = [
  { name: "Desde San Salvador", href: "/", current: false },
  // { name: "Desde Humahuaca", href: "#", current: false },
  // { name: "Desde Purmamarca", href: "#", current: false },
  // { name: "Desde la Quiaca", href: "#", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function Header() {
  return (
    <header className="w-full">
      <Disclosure as="nav" className="bg-gray-800">
        <div className="mx-auto max-w-7xl px-2 sm:px-6 lg:px-3">
          <div className="relative flex h-16 items-center justify-between">
            <div className="flex sm:w-full items-center justify-between sm:items-stretch">
              <Link href={"/"} className="flex flex-shrink-0 items-center">
                <Image
                  alt="Transportes Norte Jujuy"
                  src={JujuyImage}
                  className="h-8 w-8 rounded-full"
                  width={100}
                  height={100}
                />
                <div className=" flex flex-col items-center">
                  <p className="hidden sm:block text-xl px-1 text-white">
                    Colectivos al Norte
                  </p>
                  <div className=" w-3/4 h-1 bg-red-700"></div>
                </div>
              </Link>
              <div className="hidden sm:ml-6 sm:block">
                <div className="flex space-x-4">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      aria-current={item.current ? "page" : undefined}
                      className={classNames(
                        item.current
                          ? "bg-gray-900 text-white"
                          : "text-gray-300 hover:bg-gray-700 hover:text-white",
                        "rounded-md px-3 py-2 text-sm font-medium",
                      )}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <div className=" flex flex-col items-center">
              <p className="sm:hidden font-medium text-white">
                Colectivos al Norte
              </p>
              <div className=" w-3/4 h-1 bg-red-700"></div>
            </div>
            <div className="inset-y-0 flex items-center sm:hidden">
              {/* Mobile menu button*/}
              <DisclosureButton className="group relative inline-flex items-center justify-center rounded-md p-2 text-gray-400 hover:bg-gray-700 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Open main menu</span>
                <Bars3Icon
                  aria-hidden="true"
                  className="block h-6 w-6 group-data-[open]:hidden"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-6 w-6 group-data-[open]:block"
                />
              </DisclosureButton>
            </div>
          </div>
        </div>

        <DisclosurePanel className="sm:hidden">
          <div className="space-y-1 px-2 pb-3 pt-2">
            {navigation.map((item) => (
              <DisclosureButton
                key={item.name}
                as="a"
                href={item.href}
                aria-current={item.current ? "page" : undefined}
                className={classNames(
                  item.current
                    ? "bg-gray-900 text-white"
                    : "text-gray-300 hover:bg-gray-700 hover:text-white",
                  "block rounded-md px-3 py-2 text-base font-medium",
                )}
              >
                {item.name}
              </DisclosureButton>
            ))}
          </div>
        </DisclosurePanel>
      </Disclosure>
      <LinkCompanyPages />
    </header>
  );
}
