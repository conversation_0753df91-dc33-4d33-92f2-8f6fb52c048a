import { StaticImageData } from "next/image";

export interface IImageLink {
  imagelink: string;
}

export interface IBusSchedule {
  id: string;
  nameCompany: string;
  mondayToFridaySchedule: string[];
  saturdayAndSundaySchedule: string[];
  holidaySchedule?: string[];
  directTransport?: string[];
  updatedAgo: string;
  companyLogo: StaticImageData;
  photoSchedules: string;
  travelOrigin: string;
  destinationTravel: string;
  stations?: string;
  urlImages?: IImageLink[];
}
