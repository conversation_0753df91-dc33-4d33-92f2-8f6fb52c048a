import React from "react";
import { mockBusSchedules } from "@/mocks/busSchedules";
import CardScheduleBus from "@/components/CardScheduleBus";
import { Metadata } from "next";
import TitlePages from "@/components/TitlePages";

export const metadata: Metadata = {
  title: "Panamericano Horarios- Norte de Jujuy",
  description:
    "Horarios del colectivo Panamericano, viajes a Volcan, Purmamarca, Tilcara, Humahuaca, la Quiaca. Consulta los horarios de los colectivos al norte de Jujuy",
  keywords: [
    "Panamericano Jujuy",
    "colectivos al norte",
    "colectivos a Tilcara",
    "colectivos a Humahuaca",
    "colectivos a la Quiaca",
    "horario colectivos",
  ],
  robots: {
    index: true,
    follow: true,
  },
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
};

export default function page() {
  const panamericanoFilter = mockBusSchedules.filter(
    (bus) => bus.nameCompany === "Panamericano de Jujuy S.A",
  );
  return (
    <div>
      <TitlePages title={"Horarios del Panamericano al Norte"} />
      <CardScheduleBus mockBusSchedules={panamericanoFilter} />
    </div>
  );
}
