import React from "react";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { FunnelIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

const navigation = [
  { name: "Todos", href: "/", current: false },
  { name: "<PERSON><PERSON>", href: "/evelia", current: false },
  { name: "Santa Ana", href: "/santa-ana", current: false },
  { name: "Panamericano", href: "/panamericano", current: false },
  { name: "El Quiaqueño", href: "/el-quiaquenio", current: false },
  { name: "Vientos del Norte", href: "/vientos-del-norte", current: false },
  { name: "Jamabus", href: "/jamabus", current: false },
  { name: "Balut", href: "/balut", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function LinkCompanyPages() {
  return (
    <div className="w-full">
      <Disclosure as="nav" className="bg-gray-300">
        <div className="mx-auto max-w-7xl">
          <div className="relative flex items-center sm:justify-between">
            <div className="flex sm:w-full items-center justify-between sm:items-stretch">
              <div className="hidden w-full sm:block">
                <div className="flex w-full justify-between">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      aria-current={item.current ? "page" : undefined}
                      className={classNames(
                        item.current
                          ? "bg-gray-900 text-white"
                          : "text-gray-800 hover:bg-gray-700 hover:text-white",
                        "px-3 py-2 w-full text-center text-sm font-medium",
                      )}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <div className="inset-y-0 flex items-center sm:hidden">
              {/* Mobile menu button*/}
              <DisclosureButton className="group relative inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-800 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white">
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Open main menu</span>
                <FunnelIcon
                  aria-hidden="true"
                  className="block h-6 w-6 group-data-[open]:hidden"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-6 w-6 group-data-[open]:block"
                />
              </DisclosureButton>
              <p className="sm:hidden text-sm text-gray-700">
                Filtrar por empresa
              </p>
            </div>
          </div>
        </div>

        <DisclosurePanel className="sm:hidden">
          <div className="space-y-1 px-2 pb-3 pt-2">
            {navigation.map((item) => (
              <DisclosureButton
                key={item.name}
                as="a"
                href={item.href}
                aria-current={item.current ? "page" : undefined}
                className={classNames(
                  item.current
                    ? "bg-gray-900 text-white"
                    : "text-gray-800 hover:bg-gray-700 hover:text-white",
                  "block rounded-md px-3 py-2 text-base font-medium",
                )}
              >
                {item.name}
              </DisclosureButton>
            ))}
          </div>
        </DisclosurePanel>
      </Disclosure>
    </div>
  );
}
