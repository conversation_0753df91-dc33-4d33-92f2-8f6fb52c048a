import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { GoogleAnalytics } from "@/components/GoogleAnalytics";
import PWAInstallPrompt from "@/components/PWAInstallPrompt";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Horario de colectivos - Norte Jujuy",
  description:
    "Horaios de colectivos al norte de Jujuy, salida desde la Terminal nueva hacia Barcena, Volcan, Purmamarca, Tilcara, Humahuaca, la Quiaca. Horarios de los colectivos de Evelia, Santa Ana, Panamericano, El Quiaqueño, Vientos del Norte, Jamabus, Balut.",
  keywords: [
    "horario colectivos norte",
    "colectivos a tilcara",
    "colectivos a purmamarca",
    "colectivos a humahuaca",
  ],
  robots: {
    index: true,
    follow: true,
  },
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
  // PWA metadata
  manifest: '/manifest.json',
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'Colectivos Norte',
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'Colectivos Norte',
    'application-name': 'Colectivos Norte Jujuy',
    'msapplication-TileColor': '#06367c',
    'theme-color': '#06367c',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <head>
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon.png" />
        <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
      </head>
      <body className={inter.className}>
        {process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS ? (
          <GoogleAnalytics ga_id={process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS} />
        ) : null}
        <Header />
        <div className="max-w-screen-xl mx-auto p-3 min-h-[100%] pt-[5rem] md:pt-28">
          {children}
        </div>
        <Footer />
        <PWAInstallPrompt />
      </body>
    </html>
  );
}
