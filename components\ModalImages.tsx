import { Dispatch, SetStateAction } from "react";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
} from "@headlessui/react";
import { XMarkIcon } from "@heroicons/react/24/outline";
import GalleryOfImages from "./GalleryOfImages";
import { IImageLink } from "@/models/busSchedules.model";

interface ModalImagesProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  dataImages: IImageLink[]
}

export default function ModalImages({ open, setOpen, dataImages }: ModalImagesProps) {
  return (
    <Dialog open={open} onClose={setOpen} className="relative z-50">
      {/* Backdrop mejorado con blur */}
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-primary-900/80 backdrop-blur-sm transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
      />

      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full items-center justify-center p-2 md:p-4">
          <DialogPanel
            transition
            className="relative w-full max-w-7xl transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95 mx-2 md:mx-4"
          >
            {/* Header del modal */}
            <div className="bg-primary-700 px-4 py-3 md:px-6 md:py-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg md:text-xl font-bold text-white">
                  Horarios de Colectivos
                </h3>
                <button
                  type="button"
                  onClick={() => setOpen(false)}
                  className="rounded-lg p-2 text-white/80 hover:bg-white/20 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-200"
                >
                  <span className="sr-only">Cerrar</span>
                  <XMarkIcon className="h-6 w-6" aria-hidden="true" />
                </button>
              </div>
            </div>

            {/* Contenido del modal */}
            <div className="bg-white p-4 md:p-6">
              <GalleryOfImages dataImages={dataImages} />
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  );
}
