import React from "react";
import Image from "next/image";
import JujuyImage from "./../assets/jujuy.png";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { Bars3Icon, XMarkIcon } from "@heroicons/react/24/outline";
import LinkCompanyPages from "./LinkCompanyPages";
import Link from "next/link";

const navigation = [
  { name: "Desde San Salvador", href: "/", current: false },
  // { name: "Desde Humahuaca", href: "#", current: false },
  // { name: "Desde Purmamarca", href: "#", current: false },
  // { name: "Desde la Quiaca", href: "#", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function Header() {
  return (
    <header className="w-full sticky top-0 z-50">
      <Disclosure as="nav" className="bg-gradient-hero shadow-corporate border-b border-primary-200">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="relative flex h-16 items-center justify-between">
            <div className="flex sm:w-full items-center justify-between sm:items-stretch">
              <Link href={"/"} className="flex flex-shrink-0 items-center group">
                <Image
                  alt="Transportes Norte Jujuy"
                  src={JujuyImage}
                  className="h-9 w-9 rounded-md ring-1 ring-white/20 group-hover:ring-white/30 transition-all duration-200"
                  width={100}
                  height={100}
                />
                <div className="flex flex-col items-center ml-3">
                  <p className="hidden sm:block text-lg px-1 text-white font-medium">
                    Colectivos al Norte
                  </p>
                  <div className="w-3/4 h-0.5 bg-primary-300"></div>
                </div>
              </Link>
              <div className="hidden sm:ml-6 sm:block">
                <div className="flex space-x-1">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      aria-current={item.current ? "page" : undefined}
                      className={classNames(
                        item.current
                          ? "bg-white/15 text-white border border-white/20"
                          : "text-white/85 hover:bg-white/8 hover:text-white border border-transparent",
                        "rounded-md px-3 py-2 text-sm font-medium transition-all duration-200",
                      )}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <div className="flex flex-col items-center">
              <p className="sm:hidden font-medium text-white">
                Colectivos al Norte
              </p>
              <div className="w-3/4 h-0.5 bg-primary-300"></div>
            </div>
            <div className="inset-y-0 flex items-center sm:hidden">
              {/* Mobile menu button*/}
              <DisclosureButton className="group relative inline-flex items-center justify-center rounded-md p-2 text-white/80 hover:bg-white/8 hover:text-white focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white/30 transition-all duration-200">
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Open main menu</span>
                <Bars3Icon
                  aria-hidden="true"
                  className="block h-6 w-6 group-data-[open]:hidden"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-6 w-6 group-data-[open]:block"
                />
              </DisclosureButton>
            </div>
          </div>
        </div>

        <DisclosurePanel className="sm:hidden">
          <div className="space-y-1 px-4 pb-4 pt-2 bg-primary-800/95 border-t border-white/10">
            {navigation.map((item) => (
              <DisclosureButton
                key={item.name}
                as="a"
                href={item.href}
                aria-current={item.current ? "page" : undefined}
                className={classNames(
                  item.current
                    ? "bg-white/15 text-white border border-white/20"
                    : "text-white/85 hover:bg-white/8 hover:text-white border border-transparent",
                  "block rounded-md px-4 py-2.5 text-base font-medium transition-all duration-200",
                )}
              >
                {item.name}
              </DisclosureButton>
            ))}
          </div>
        </DisclosurePanel>
      </Disclosure>
      <LinkCompanyPages />
    </header>
  );
}
