'use client';

import { useEffect, useState } from 'react';

interface PWAStatus {
  isSupported: boolean;
  isInstalled: boolean;
  isOnline: boolean;
  swRegistration: ServiceWorkerRegistration | null;
}

export function usePWA() {
  const [status, setStatus] = useState<PWAStatus>({
    isSupported: false,
    isInstalled: false,
    isOnline: true,
    swRegistration: null,
  });

  useEffect(() => {
    // Solo ejecutar en el cliente
    if (typeof window === 'undefined') return;

    // Verificar soporte para Service Workers
    const isSupported = 'serviceWorker' in navigator;

    // Verificar si está instalado (modo standalone)
    const isInstalled = window.matchMedia('(display-mode: standalone)').matches ||
                       (window.navigator as any).standalone ||
                       document.referrer.includes('android-app://');

    // Estado inicial de conexión
    const isOnline = navigator.onLine;

    setStatus(prev => ({
      ...prev,
      isSupported,
      isInstalled,
      isOnline,
    }));

    // Registrar Service Worker si es soportado
    if (isSupported) {
      registerServiceWorker();
    }

    // Listeners para cambios de conectividad
    const handleOnline = () => setStatus(prev => ({ ...prev, isOnline: true }));
    const handleOffline = () => setStatus(prev => ({ ...prev, isOnline: false }));

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const registerServiceWorker = async () => {
    // Solo registrar en el cliente y si hay soporte
    if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
      return;
    }

    try {
      const registration = await navigator.serviceWorker.register('/sw.js', {
        scope: '/',
        updateViaCache: 'none',
      });

      setStatus(prev => ({ ...prev, swRegistration: registration }));

      // Manejar actualizaciones del Service Worker
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // Nueva versión disponible
              console.log('🔄 Nueva versión de la PWA disponible');
              showUpdateNotification();
            }
          });
        }
      });

      console.log('✅ Service Worker registrado exitosamente');
    } catch (error) {
      console.error('❌ Error registrando Service Worker:', error);
    }
  };

  const showUpdateNotification = () => {
    // Mostrar notificación de actualización disponible
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('Colectivos Norte Jujuy', {
        body: 'Nueva versión disponible. Recarga la página para actualizar.',
        icon: '/icons/icon-192x192.png',
        badge: '/icons/icon-96x96.png',
        tag: 'app-update',
      });
    }
  };

  const updateServiceWorker = () => {
    if (status.swRegistration?.waiting) {
      status.swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
      window.location.reload();
    }
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    }
    return false;
  };

  return {
    ...status,
    updateServiceWorker,
    requestNotificationPermission,
  };
}
