import Image from "next/image";
import ListScheduleHours from "./ListScheduleHours";
import { MdOutlineUpdate } from "react-icons/md";
import AdditionalInformation from "./AdditionalInformation";
import { IBusSchedule } from "@/models/busSchedules.model";

interface CardScheduleBusProps {
  mockBusSchedules: IBusSchedule[];
}

export default function CardScheduleBus({
  mockBusSchedules,
}: CardScheduleBusProps) {
  return (
    <div>
      {mockBusSchedules.map((company: IBusSchedule) => {
        return (
          <div key={company.id} className="shadow-md relative mb-8">
            <div className="absolute top-2 right-2 flex flex-col items-center text-sm text-gray-400">
              <div className="flex items-center">
                <MdOutlineUpdate className="text-base" />
                Ult. actualización
              </div>
              <p> {company.updatedAgo}</p>
            </div>
            <div className="px-4 border">
              <Image
                className="w-auto"
                src={company.companyLogo}
                alt={company.nameCompany}
                width={100}
                height={100}
              />
              <h3 className="text-base font-semibold leading-7 text-gray-900">
                {company.nameCompany}
              </h3>
              <p className="mt-1 max-w-2xl text-sm leading-6 text-gray-500">
                <span>
                  Salida de{" "}
                  <span className="text-blue-950 font-medium">
                    {company.travelOrigin}
                  </span>{" "}
                  a{" "}
                  <span className="text-blue-950 font-medium">
                    {company.destinationTravel}
                  </span>
                </span>
              </p>
            </div>
            <div className="mt-6 border-t px-4 border-gray-100">
              <dl className="divide-y divide-gray-100">
                <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-gray-900">
                    De Lunes a Viernes
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <ListScheduleHours
                      company={company.mondayToFridaySchedule}
                    />
                  </dd>
                </div>
                <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                  <dt className="text-sm font-medium leading-6 text-gray-900">
                    Sabados y Domingos
                  </dt>
                  <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                    <ListScheduleHours
                      company={company.saturdayAndSundaySchedule}
                    />
                  </dd>
                </div>
                {company.stations && company.stations?.length > 0 && (
                  <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                    <dt className="text-sm font-medium leading-6 text-gray-900">
                      Paradas
                    </dt>
                    <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                      {company.stations}
                    </dd>
                  </div>
                )}
                {company.directTransport &&
                  company.directTransport?.length > 0 && (
                    <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
                      <dt className="text-sm font-medium leading-6 text-gray-900">
                        Colectivos directos
                      </dt>
                      <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
                        <ListScheduleHours company={company.directTransport} />
                      </dd>
                    </div>
                  )}
                <AdditionalInformation dataImages={company.urlImages || []} />
              </dl>
            </div>
          </div>
        );
      })}
    </div>
  );
}
