import Image from "next/image";
import ListScheduleHours from "./ListScheduleHours";
import { MdOutlineUpdate } from "react-icons/md";
import AdditionalInformation from "./AdditionalInformation";
import { IBusSchedule } from "@/models/busSchedules.model";

interface CardScheduleBusProps {
  mockBusSchedules: IBusSchedule[];
}

export default function CardScheduleBus({
  mockBusSchedules,
}: CardScheduleBusProps) {
  return (
    <div className="space-y-8">
      {mockBusSchedules.map((company: IBusSchedule) => {
        return (
          <div key={company.id} className="bg-gradient-card shadow-medium hover:shadow-strong relative rounded-2xl overflow-hidden border border-neutral-200/50 transition-all duration-300 hover:scale-[1.02]">
            <div className="absolute top-4 right-4 flex flex-col items-center text-xs text-neutral-500 bg-white/80 backdrop-blur-sm rounded-xl px-3 py-2 shadow-soft">
              <div className="flex items-center gap-1">
                <MdOutlineUpdate className="text-sm text-primary-500" />
                <span className="font-medium">Ult. actualización</span>
              </div>
              <p className="text-neutral-600 font-semibold">{company.updatedAgo}</p>
            </div>
            <div className="px-6 py-6 bg-gradient-to-r from-primary-50 to-secondary-50 border-b border-neutral-200/50">
              <div className="flex items-center gap-4">
                <div className="bg-white rounded-xl p-3 shadow-soft">
                  <Image
                    className="w-auto h-12"
                    src={company.companyLogo}
                    alt={company.nameCompany}
                    width={100}
                    height={100}
                  />
                </div>
                <div>
                  <h3 className="text-xl font-bold leading-7 text-neutral-900">
                    {company.nameCompany}
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm leading-6 text-neutral-600">
                    <span>
                      Salida de{" "}
                      <span className="text-primary-700 font-semibold bg-primary-100 px-2 py-1 rounded-lg">
                        {company.travelOrigin}
                      </span>{" "}
                      a{" "}
                      <span className="text-earth-700 font-semibold bg-earth-100 px-2 py-1 rounded-lg">
                        {company.destinationTravel}
                      </span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div className="px-6 py-6">
              <dl className="space-y-6">
                <div className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-xl p-4 border border-primary-200/50">
                  <dt className="text-sm font-semibold leading-6 text-primary-900 mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-primary-500 rounded-full"></div>
                    De Lunes a Viernes
                  </dt>
                  <dd className="text-sm leading-6 text-neutral-700">
                    <ListScheduleHours
                      company={company.mondayToFridaySchedule}
                    />
                  </dd>
                </div>
                <div className="bg-gradient-to-r from-secondary-50 to-secondary-100 rounded-xl p-4 border border-secondary-200/50">
                  <dt className="text-sm font-semibold leading-6 text-secondary-900 mb-3 flex items-center gap-2">
                    <div className="w-2 h-2 bg-secondary-500 rounded-full"></div>
                    Sábados y Domingos
                  </dt>
                  <dd className="text-sm leading-6 text-neutral-700">
                    <ListScheduleHours
                      company={company.saturdayAndSundaySchedule}
                    />
                  </dd>
                </div>
                {company.stations && company.stations?.length > 0 && (
                  <div className="bg-gradient-to-r from-neutral-50 to-neutral-100 rounded-xl p-4 border border-neutral-200/50">
                    <dt className="text-sm font-semibold leading-6 text-neutral-900 mb-3 flex items-center gap-2">
                      <div className="w-2 h-2 bg-neutral-500 rounded-full"></div>
                      Paradas
                    </dt>
                    <dd className="text-sm leading-6 text-neutral-700 bg-white rounded-lg p-3 shadow-soft">
                      {company.stations}
                    </dd>
                  </div>
                )}
                {company.directTransport &&
                  company.directTransport?.length > 0 && (
                    <div className="bg-gradient-to-r from-success-50 to-success-100 rounded-xl p-4 border border-success-200/50">
                      <dt className="text-sm font-semibold leading-6 text-success-900 mb-3 flex items-center gap-2">
                        <div className="w-2 h-2 bg-success-500 rounded-full"></div>
                        Colectivos Directos
                      </dt>
                      <dd className="text-sm leading-6 text-neutral-700">
                        <ListScheduleHours company={company.directTransport} />
                      </dd>
                    </div>
                  )}
                <AdditionalInformation dataImages={company.urlImages || []} />
              </dl>
            </div>
          </div>
        );
      })}
    </div>
  );
}
