"use client";
import React, { useState } from "react";
import { PhotoIcon } from "@heroicons/react/24/outline";
import ModalImages from "./ModalImages";
import { IImageLink } from "@/models/busSchedules.model";

export default function AdditionalInformation({dataImages} : {dataImages: IImageLink[]}) {
  const [openModal, setOpenModal] = useState(false);

  return (
    <>
      {/* Botón de imágenes siempre visible y prominente */}
      <div>
        <button
          onClick={() => setOpenModal(true)}
          className="w-full bg-primary-700 hover:bg-primary-800 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98]"
        >
          <PhotoIcon className="h-5 w-5" />
          <span>Ver Capturas de Boletería</span>
          <span className="bg-white/20 text-xs px-2 py-1 rounded-full">
            {dataImages?.length || 0} fotos
          </span>
        </button>
      </div>

      {/* Modal de imágenes */}
      <ModalImages open={openModal} setOpen={setOpenModal} dataImages={dataImages} />
    </>
  );
}
