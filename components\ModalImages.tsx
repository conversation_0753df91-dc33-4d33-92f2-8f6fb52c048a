import { Dispatch, SetStateAction } from "react";
import {
  Dialog,
  DialogBackdrop,
  DialogPanel,
} from "@headlessui/react";
import { FaWindowClose } from "react-icons/fa";
import GalleryOfImages from "./GalleryOfImages";
import { IImageLink } from "@/models/busSchedules.model";

interface ModalImagesProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
  dataImages: IImageLink[]
}

export default function ModalImages({ open, setOpen, dataImages }: ModalImagesProps) {
  return (
    <Dialog open={open} onClose={setOpen} className="relative z-10">
      <DialogBackdrop
        transition
        className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in"
      />

      <div className="fixed inset-0 z-10 w-screen overflow-y-auto">
        <div className="flex min-h-full justify-center p-4 text-center">
          <DialogPanel
            transition
            className="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all data-[closed]:translate-y-4 data-[closed]:opacity-0 data-[enter]:duration-300 data-[leave]:duration-200 data-[enter]:ease-out data-[leave]:ease-in sm:my-8 sm:w-full data-[closed]:sm:translate-y-0 data-[closed]:sm:scale-95"
          >
            <div className="bg-white flex justify-center px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <GalleryOfImages dataImages={dataImages} />
              </div>
            </div>
            <div>
              <FaWindowClose
                className="text-red-600 text-2xl absolute top-0 right-2 cursor-pointer"
                onClick={() => setOpen(false)}
              />
            </div>
          </DialogPanel>
        </div>
      </div>
    </Dialog>
  );
}
