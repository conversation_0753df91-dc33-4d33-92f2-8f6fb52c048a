import Image from "next/image";
import ListScheduleHours from "./ListScheduleHours";
import { MdOutlineUpdate } from "react-icons/md";
import AdditionalInformation from "./AdditionalInformation";
import { IBusSchedule } from "@/models/busSchedules.model";

interface CardScheduleBusProps {
  mockBusSchedules: IBusSchedule[];
}

export default function CardScheduleBus({
  mockBusSchedules,
}: CardScheduleBusProps) {
  return (
    <div className="space-y-6">
      {mockBusSchedules.map((company: IBusSchedule) => {
        return (
          <div key={company.id} className="bg-white shadow-corporate hover:shadow-medium relative rounded-lg overflow-hidden border border-neutral-300 transition-all duration-200">
            <div className="absolute top-3 right-3 flex flex-col items-center text-xs text-neutral-500 bg-neutral-50 border border-neutral-200 rounded-md px-3 py-2">
              <div className="flex items-center gap-1">
                <MdOutlineUpdate className="text-sm text-primary-700" />
                <span className="font-medium">Ult. actualización</span>
              </div>
              <p className="text-neutral-700 font-medium">{company.updatedAgo}</p>
            </div>
            <div className="px-6 py-5 bg-gradient-corporate border-b border-neutral-200">
              <div className="flex items-center gap-4">
                <div className="bg-white rounded-md p-3 shadow-corporate border border-neutral-200">
                  <Image
                    className="w-auto h-10"
                    src={company.companyLogo}
                    alt={company.nameCompany}
                    width={100}
                    height={100}
                  />
                </div>
                <div>
                  <h3 className="text-lg font-semibold leading-7 text-neutral-900">
                    {company.nameCompany}
                  </h3>
                  <p className="mt-1 max-w-2xl text-sm leading-6 text-neutral-600">
                    <span>
                      Salida de{" "}
                      <span className="text-primary-700 font-medium bg-primary-50 px-2 py-0.5 rounded border border-primary-200">
                        {company.travelOrigin}
                      </span>{" "}
                      a{" "}
                      <span className="text-primary-700 font-medium bg-primary-100 px-2 py-0.5 rounded border border-primary-300">
                        {company.destinationTravel}
                      </span>
                    </span>
                  </p>
                </div>
              </div>
            </div>
            <div className="px-6 py-5">
              <dl className="space-y-5">
                <div className="bg-primary-50 rounded-md p-4 border border-primary-200">
                  <dt className="text-sm font-semibold leading-6 text-primary-700 mb-3 flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-primary-700 rounded-full"></div>
                    De Lunes a Viernes
                  </dt>
                  <dd className="text-sm leading-6 text-neutral-700">
                    <ListScheduleHours
                      company={company.mondayToFridaySchedule}
                    />
                  </dd>
                </div>
                <div className="bg-primary-100 rounded-md p-4 border border-primary-300">
                  <dt className="text-sm font-semibold leading-6 text-primary-700 mb-3 flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-primary-700 rounded-full"></div>
                    Sábados y Domingos
                  </dt>
                  <dd className="text-sm leading-6 text-neutral-700">
                    <ListScheduleHours
                      company={company.saturdayAndSundaySchedule}
                    />
                  </dd>
                </div>
                {company.stations && company.stations?.length > 0 && (
                  <div className="bg-neutral-50 rounded-md p-4 border border-neutral-200">
                    <dt className="text-sm font-semibold leading-6 text-neutral-900 mb-3 flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-neutral-600 rounded-full"></div>
                      Paradas
                    </dt>
                    <dd className="text-sm leading-6 text-neutral-700 bg-white rounded-md p-3 border border-neutral-200">
                      {company.stations}
                    </dd>
                  </div>
                )}
                {company.directTransport &&
                  company.directTransport?.length > 0 && (
                    <div className="bg-success-50 rounded-md p-4 border border-success-200">
                      <dt className="text-sm font-semibold leading-6 text-success-800 mb-3 flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-success-600 rounded-full"></div>
                        Colectivos Directos
                      </dt>
                      <dd className="text-sm leading-6 text-neutral-700">
                        <ListScheduleHours company={company.directTransport} />
                      </dd>
                    </div>
                  )}
                <AdditionalInformation dataImages={company.urlImages || []} />
              </dl>
            </div>
          </div>
        );
      })}
    </div>
  );
}
