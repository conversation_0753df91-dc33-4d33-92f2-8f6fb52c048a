"use client";

import { IImageLink } from "@/models/busSchedules.model";
import Image from "next/image";
import React, { useState } from "react";
import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";

export default function GalleryOfImages({
  dataImages,
}: {
  dataImages: IImageLink[];
}) {
  const firstImage: string = dataImages[0]
    ? dataImages[0].imagelink
    : "https://i.pinimg.com/564x/9e/57/83/9e5783dc9b73be938b0b1942a7fd9b24.jpg";

  const [active, setActive] = useState(firstImage);
  const [currentIndex, setCurrentIndex] = useState(0);

  const handleImageSelect = (imagelink: string, index: number) => {
    setActive(imagelink);
    setCurrentIndex(index);
  };

  const handlePrevious = () => {
    const newIndex = currentIndex > 0 ? currentIndex - 1 : dataImages.length - 1;
    setCurrentIndex(newIndex);
    setActive(dataImages[newIndex].imagelink);
  };

  const handleNext = () => {
    const newIndex = currentIndex < dataImages.length - 1 ? currentIndex + 1 : 0;
    setCurrentIndex(newIndex);
    setActive(dataImages[newIndex].imagelink);
  };

  if (!dataImages || dataImages.length === 0) {
    return (
      <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
        <p className="text-gray-500">No hay imágenes disponibles</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-6xl mx-auto">
      {/* Imagen principal */}
      <div className="mb-4 md:mb-6">
        <div className="relative aspect-[4/3] md:aspect-[16/10] w-full overflow-hidden rounded-xl bg-gray-100">
          <Image
            src={active}
            alt={`Horario de colectivos - Imagen ${currentIndex + 1}`}
            fill
            className="object-contain transition-all duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
            priority
          />
        </div>

        {/* Controles de navegación debajo de la imagen - Solo si hay más de una imagen */}
        {dataImages.length > 1 && (
          <div className="flex items-center justify-between mt-4">
            <button
              onClick={handlePrevious}
              className="flex items-center gap-2 bg-primary-700 hover:bg-primary-800 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
              aria-label="Imagen anterior"
            >
              <ChevronLeftIcon className="h-5 w-5" />
              <span className="hidden sm:inline">Anterior</span>
            </button>

            {/* Indicador de posición centrado */}
            <div className="bg-gray-100 text-primary-700 px-4 py-2 rounded-lg font-semibold text-sm border border-gray-200">
              {currentIndex + 1} / {dataImages.length}
            </div>

            <button
              onClick={handleNext}
              className="flex items-center gap-2 bg-primary-700 hover:bg-primary-800 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl"
              aria-label="Siguiente imagen"
            >
              <span className="hidden sm:inline">Siguiente</span>
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>

      {/* Miniaturas - Solo mostrar si hay más de una imagen */}
      {dataImages.length > 1 && (
        <div className="grid grid-cols-3 md:grid-cols-5 lg:grid-cols-6 gap-2 md:gap-3">
          {dataImages.map(({ imagelink }, index) => (
            <button
              key={index}
              onClick={() => handleImageSelect(imagelink, index)}
              className={`relative aspect-square overflow-hidden rounded-lg transition-all duration-200 ${
                active === imagelink
                  ? "ring-2 ring-primary-600 ring-offset-2 scale-105"
                  : "hover:scale-105 hover:ring-1 hover:ring-primary-400"
              }`}
            >
              <Image
                src={imagelink}
                alt={`Miniatura ${index + 1}`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 33vw, (max-width: 1200px) 20vw, 16vw"
              />
              {active === imagelink && (
                <div className="absolute inset-0 bg-primary-600/20 flex items-center justify-center">
                  <div className="w-3 h-3 bg-white rounded-full shadow-lg"></div>
                </div>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
