@tailwind base;
@tailwind components;
@tailwind utilities;

html {
  height: 100%;
  scroll-behavior: smooth;
}

body {
  height: 80%;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-feature-settings: 'rlig' 1, 'calt' 1;
}

/* Mejoras de accesibilidad y UX */
@layer base {
  * {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-white;
  }

  button, a {
    @apply transition-all duration-300 ease-in-out;
  }
}

/* Animaciones personalizadas */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .animate-bounce-subtle {
    animation: bounceSubtle 2s infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceSubtle {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

/* Scrollbar personalizado */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-neutral-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-500;
}
