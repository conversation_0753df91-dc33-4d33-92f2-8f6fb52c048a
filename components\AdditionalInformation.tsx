"use client";
import Link from "next/link";
import React, { useState } from "react";
import { IoIosArrowDown, IoIosArrowUp } from "react-icons/io";
import ModalImages from "./ModalImages";
import { IImageLink } from "@/models/busSchedules.model";

export default function AdditionalInformation({dataImages} : {dataImages: IImageLink[]}) {
  const [open, setOpen] = useState(false);
  const [openModal, setOpenModal] = useState(false);

  return (
    <>
      <div className="py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
        <dt className="text-sm font-medium leading-6 text-gray-900">
          <div
            onClick={() => setOpen(!open)}
            className="flex items-center text-blue-700 cursor-pointer w-[160px]"
          >
            <p>Ver mas informacion</p>
            {!open && <IoIosArrowDown />}
            {open && <IoIosArrowUp />}
          </div>
        </dt>
      </div>
      {open && (
        <div>
          <div className="px-4 py-2 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
            <dt className="text-sm font-medium leading-6 text-gray-900">
              Captura de Boleteria de la terminal
            </dt>
            <dd className="mt-1 text-sm leading-6 text-gray-700 sm:col-span-2 sm:mt-0">
              <div className="text-blue-700 cursor-pointer">
                <button onClick={() => setOpenModal(true)}>
                  Abrir Imagenes
                </button>
                <ModalImages open={openModal} setOpen={setOpenModal} dataImages={dataImages} />
              </div>
            </dd>
          </div>
        </div>
      )}
    </>
  );
}
