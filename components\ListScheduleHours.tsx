import React from "react";

interface IListScheduleHoursProps {
  company: string[] | undefined;
}

export default function ListScheduleHours({
  company,
}: IListScheduleHoursProps) {
  return (
    <ul className="flex flex-wrap gap-2">
      {company?.map((hour, i) => {
        return (
          <li
            key={i}
            className="px-3 py-1.5 bg-white border border-neutral-300 hover:border-primary-400 rounded-md shadow-corporate hover:shadow-medium transition-all duration-200 cursor-default group"
          >
            <p className="text-neutral-800 font-medium text-sm group-hover:text-primary-700 transition-colors duration-200">
              {hour}
            </p>
          </li>
        );
      })}
    </ul>
  );
}
