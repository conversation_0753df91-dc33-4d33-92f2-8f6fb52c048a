"use client";

import { IImageLink } from "@/models/busSchedules.model";
import Image from "next/image";
import React from "react";

export default function GalleryOfImages({
  dataImages,
}: {
  dataImages: IImageLink[];
}) {
  const firstImage : string = dataImages[0]
    ? dataImages[0].imagelink
    : "https://i.pinimg.com/564x/9e/57/83/9e5783dc9b73be938b0b1942a7fd9b24.jpg";

  const [active, setActive] = React.useState(firstImage);

  return (
    <div className="grid gap-4">
      <div className="w-full flex justify-center">
        <Image
          className="w-auto rounded-lg object-cover object-center md:h-[480px]"
          src={active}
          alt="foto-horarios-norte-jujuy"
          width={300}
          height={200}
        />
      </div>
      <div className="grid grid-cols-5 gap-4">
        {dataImages.map(({ imagelink }, index) => (
          <div key={index}>
            <Image
              onClick={() => setActive(imagelink)}
              src={imagelink}
              className="h-20 w-auto cursor-pointer rounded-lg object-cover object-center"
              alt="gallery-image"
              width={100}
              height={200}
            />
          </div>
        ))}
      </div>
    </div>
  );
}
