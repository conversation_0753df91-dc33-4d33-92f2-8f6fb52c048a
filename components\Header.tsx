import React from "react";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { Bars3Icon, XMarkIcon, FunnelIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import Image from "next/image";
import ColectivoBlanco from "../assets/logo/colectivo_blanco.png";

const companyNavigation = [
  { name: "Todos", href: "/", current: false },
  { name: "Eve<PERSON>", href: "/evelia", current: false },
  { name: "Santa Ana", href: "/santa-ana", current: false },
  { name: "Panamericano", href: "/panamericano", current: false },
  { name: "El Quiaqueño", href: "/el-quiaquenio", current: false },
  { name: "Vientos del Norte", href: "/vientos-del-norte", current: false },
  { name: "<PERSON><PERSON><PERSON>", href: "/jamabus", current: false },
  { name: "<PERSON><PERSON>", href: "/balut", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}



export default function Header() {
  return (
    <header className="w-full fixed top-0 z-50">
      <Disclosure as="nav" className="bg-primary-700 shadow-2xl border-b border-primary-600">
        <div className="mx-auto max-w-7xl px-4 md:px-6 lg:px-8">
          <div className="relative flex h-18 items-center justify-between">
            {/* Logo - siempre visible */}
            <div className="flex flex-shrink-0 items-center">
              <Link href={"/"} className="flex items-center group">
                <Image
                  src={ColectivoBlanco}
                  alt="Colectivos Norte Jujuy"
                  className="h-8 w-8 group-hover:scale-105 transition-all duration-300"
                  width={32}
                  height={32}
                />
                <div className="flex flex-col ml-3">
                  <p className="hidden md:block text-lg font-bold text-white tracking-wide">
                    Colectivos al Norte
                  </p>
                  <div className="hidden md:block w-full h-0.5 bg-gradient-to-r from-white/80 via-white/40 to-transparent mt-1"></div>
                </div>
              </Link>
            </div>

            {/* Título centrado en móvil */}
            <div className="flex flex-col items-center md:hidden">
              <p className="font-bold text-white text-base tracking-wide">
                Colectivos al Norte
              </p>
              <div className="w-20 h-0.5 bg-gradient-to-r from-white/80 via-white/40 to-white/80 mt-1"></div>
            </div>

            {/* Información de origen */}
            <div className="hidden md:flex md:ml-6 items-center">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-white/60 rounded-full"></div>
                <p className="text-white/90 text-sm font-medium tracking-wide">
                  Desde San Salvador de Jujuy
                </p>
              </div>
            </div>

            {/* Botón menú móvil */}
            <div className="flex items-center md:hidden">
              <DisclosureButton className="group relative inline-flex items-center justify-center rounded-lg p-2.5 text-white hover:bg-white/15 hover:text-white focus:outline-none focus:ring-2 focus:ring-white/40 transition-all duration-300 border border-white/20 backdrop-blur-sm shadow-lg hover:shadow-xl">
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Open main menu</span>
                <Bars3Icon
                  aria-hidden="true"
                  className="block h-5 w-5 group-data-[open]:hidden"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-5 w-5 group-data-[open]:block"
                />
              </DisclosureButton>
            </div>
          </div>
        </div>

        <DisclosurePanel className="md:hidden">
          <div className="bg-primary-800 border-t border-primary-600">
            {/* Información de origen */}
            <div className="flex items-center justify-center gap-2 py-3 border-b border-primary-700">
              <div className="w-2 h-2 bg-white/60 rounded-full"></div>
              <p className="text-white/90 text-sm font-medium tracking-wide">
                Desde San Salvador de Jujuy
              </p>
            </div>

            {/* Filtros por empresa */}
            <div className="px-4 py-4">
              <h3 className="text-sm font-semibold text-white flex items-center gap-2 mb-3">
                <FunnelIcon className="h-4 w-4 text-white" />
                Filtrar por empresa
              </h3>
              <div className="space-y-1">
                {companyNavigation.map((item) => (
                  <DisclosureButton
                    key={item.name}
                    as="a"
                    href={item.href}
                    aria-current={item.current ? "page" : undefined}
                    className={classNames(
                      item.current
                        ? "bg-white/20 text-white"
                        : "text-white/90 hover:bg-white/10 hover:text-white",
                      "block rounded-md px-4 py-2.5 text-sm font-medium transition-all duration-200",
                    )}
                  >
                    {item.name}
                  </DisclosureButton>
                ))}
              </div>
            </div>
          </div>
        </DisclosurePanel>
      </Disclosure>
    </header>
  );
}
