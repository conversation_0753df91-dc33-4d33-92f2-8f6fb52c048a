const sharp = require('sharp');
const fs = require('fs');
const path = require('path');

// Tamaños de iconos necesarios para PWA
const iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];

// Rutas
const inputPath = path.join(__dirname, '../assets/logo/favicon.png');
const outputDir = path.join(__dirname, '../public/icons');

// Crear directorio si no existe
if (!fs.existsSync(outputDir)) {
  fs.mkdirSync(outputDir, { recursive: true });
}

async function generateIcons() {
  console.log('🎨 Regenerando iconos PWA con logo oficial...');
  console.log(`📁 Usando logo: ${inputPath}`);

  try {
    // Verificar que el archivo fuente existe
    if (!fs.existsSync(inputPath)) {
      throw new Error(`Archivo fuente no encontrado: ${inputPath}`);
    }

    // Limpiar iconos existentes
    console.log('🧹 Limpiando iconos existentes...');
    const existingIcons = fs.readdirSync(outputDir).filter(file => file.startsWith('icon-'));
    for (const icon of existingIcons) {
      fs.unlinkSync(path.join(outputDir, icon));
      console.log(`🗑️ Eliminado: ${icon}`);
    }

    // Generar cada tamaño de icono
    console.log('🎨 Generando nuevos iconos...');
    for (const size of iconSizes) {
      const outputPath = path.join(outputDir, `icon-${size}x${size}.png`);

      await sharp(inputPath)
        .resize(size, size, {
          fit: 'contain',
          background: { r: 255, g: 255, b: 255, alpha: 1 } // Fondo blanco para mejor contraste
        })
        .png({ quality: 100, compressionLevel: 0 })
        .toFile(outputPath);

      console.log(`✅ Generado: icon-${size}x${size}.png`);
    }

    // Generar favicon.png (reemplazar el existente)
    const faviconPath = path.join(__dirname, '../public/favicon.png');
    if (fs.existsSync(faviconPath)) {
      fs.unlinkSync(faviconPath);
      console.log('🗑️ Eliminado favicon.png existente');
    }

    await sharp(inputPath)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .png({ quality: 100 })
      .toFile(faviconPath);

    console.log('✅ Generado: favicon.png');

    // Generar apple-touch-icon (reemplazar el existente)
    const appleTouchIconPath = path.join(__dirname, '../public/apple-touch-icon.png');
    if (fs.existsSync(appleTouchIconPath)) {
      fs.unlinkSync(appleTouchIconPath);
      console.log('🗑️ Eliminado apple-touch-icon.png existente');
    }

    await sharp(inputPath)
      .resize(180, 180, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .png({ quality: 100 })
      .toFile(appleTouchIconPath);

    console.log('✅ Generado: apple-touch-icon.png');

    // Generar favicon.ico adicional
    const faviconIcoPath = path.join(__dirname, '../public/favicon.ico');
    await sharp(inputPath)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 255, g: 255, b: 255, alpha: 1 }
      })
      .png()
      .toFile(faviconIcoPath.replace('.ico', '_temp.png'));

    // Renombrar para favicon.ico (algunos navegadores lo prefieren)
    if (fs.existsSync(faviconIcoPath.replace('.ico', '_temp.png'))) {
      if (fs.existsSync(faviconIcoPath)) {
        fs.unlinkSync(faviconIcoPath);
      }
      fs.renameSync(faviconIcoPath.replace('.ico', '_temp.png'), faviconIcoPath.replace('.ico', '.png'));
      console.log('✅ Generado: favicon para navegadores');
    }

    console.log('🎉 ¡Todos los iconos PWA regenerados exitosamente con el logo oficial!');
    console.log(`📊 Total de iconos generados: ${iconSizes.length + 3}`);

  } catch (error) {
    console.error('❌ Error generando iconos:', error.message);
    process.exit(1);
  }
}

generateIcons();
