import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { GoogleAnalytics } from "@/components/GoogleAnalytics";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Horaio de colectivos - Norte Jujuy",
  description:
    "Horaios de colectivos al norte de Jujuy, horarios del Santa Ana, Evelia, Panamericano, El Quiaqueño, Vientos del Norte. Colectivos al Norte de Jujuy. Colectivos a Purmamarca, colectivos a Tilcara, colectivos a Humahuaca, Colectivos a la Quiaca",
  keywords: [
    "horario colectivos norte",
    "colectivos a tilcara",
    "colectivos a purmamarca",
    "colectivos a humahuaca",
  ],
  robots: {
    index: true,
    follow: true,
  },
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        {process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS ? (
          <GoogleAnalytics ga_id={process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS} />
        ) : null}
        <Header />
        <div className="max-w-screen-xl mx-auto p-3 min-h-[100%] pt-20">
          {children}
        </div>
        <Footer />
      </body>
    </html>
  );
}
