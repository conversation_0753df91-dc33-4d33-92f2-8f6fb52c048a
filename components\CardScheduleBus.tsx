import Image from "next/image";
import ListScheduleHours from "./ListScheduleHours";
import { MdOutlineUpdate } from "react-icons/md";
import AdditionalInformation from "./AdditionalInformation";
import { IBusSchedule } from "@/models/busSchedules.model";

interface CardScheduleBusProps {
  mockBusSchedules: IBusSchedule[];
}

export default function CardScheduleBus({
  mockBusSchedules,
}: CardScheduleBusProps) {
  return (
    <div className="space-y-6">
      {mockBusSchedules.map((company: IBusSchedule) => {
        return (
          <div key={company.id} className="bg-white shadow-corporate hover:shadow-medium relative rounded-lg overflow-hidden border border-neutral-300 transition-all duration-200">
            {/* Header con diseño responsive mejorado */}
            <div className="relative px-4 sm:px-6 py-4 sm:py-5 bg-gradient-corporate border-b border-neutral-200">
              {/* Información de última actualización - responsive */}
              <div className="absolute top-2 right-2 sm:top-3 sm:right-3 flex flex-col items-end text-xs text-neutral-500 bg-neutral-50/90 backdrop-blur-sm border border-neutral-200 rounded-md px-2 py-1.5 sm:px-3 sm:py-2 z-10">
                <div className="flex items-center gap-1">
                  <MdOutlineUpdate className="text-xs sm:text-sm text-primary-700" />
                  <span className="font-medium hidden sm:inline">Ult. actualización</span>
                  <span className="font-medium sm:hidden">Actualizado</span>
                </div>
                <p className="text-neutral-700 font-medium text-xs">{company.updatedAgo}</p>
              </div>

              {/* Layout principal del header - responsive */}
              <div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 pr-20 sm:pr-32">
                {/* Logo mejorado - aprovechando PNG sin fondo */}
                <div className="flex-shrink-0 self-start sm:self-center">
                  <div className="relative">
                    <Image
                      className="w-auto h-12 sm:h-14 drop-shadow-sm"
                      src={company.companyLogo}
                      alt={company.nameCompany}
                      width={120}
                      height={120}
                    />
                  </div>
                </div>

                {/* Información de la empresa */}
                <div className="flex-1 min-w-0">
                  <h3 className="text-base sm:text-lg font-semibold leading-6 sm:leading-7 text-neutral-900 mb-2">
                    {company.nameCompany}
                  </h3>

                  {/* Información de ruta - responsive */}
                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-2 text-xs sm:text-sm">
                    <span className="text-neutral-600">Salida de</span>
                    <span className="text-primary-700 font-medium bg-primary-50 px-2 py-0.5 rounded border border-primary-200 inline-block w-fit">
                      {company.travelOrigin}
                    </span>
                    <span className="text-neutral-600 hidden sm:inline">a</span>
                    <span className="text-neutral-600 sm:hidden">↓</span>
                    <span className="text-primary-700 font-medium bg-primary-100 px-2 py-0.5 rounded border border-primary-300 inline-block w-fit">
                      {company.destinationTravel}
                    </span>
                  </div>
                </div>
              </div>
            </div>
            <div className="px-4 sm:px-6 py-4 sm:py-5">
              <dl className="space-y-4 sm:space-y-5">
                <div className="bg-primary-50 rounded-md p-3 sm:p-4 border border-primary-200">
                  <dt className="text-sm font-semibold leading-6 text-primary-700 mb-2 sm:mb-3 flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-primary-700 rounded-full"></div>
                    De Lunes a Viernes
                  </dt>
                  <dd className="text-sm leading-6 text-neutral-700">
                    <ListScheduleHours
                      company={company.mondayToFridaySchedule}
                    />
                  </dd>
                </div>
                <div className="bg-primary-100 rounded-md p-3 sm:p-4 border border-primary-300">
                  <dt className="text-sm font-semibold leading-6 text-primary-700 mb-2 sm:mb-3 flex items-center gap-2">
                    <div className="w-1.5 h-1.5 bg-primary-700 rounded-full"></div>
                    Sábados y Domingos
                  </dt>
                  <dd className="text-sm leading-6 text-neutral-700">
                    <ListScheduleHours
                      company={company.saturdayAndSundaySchedule}
                    />
                  </dd>
                </div>
                {company.stations && company.stations?.length > 0 && (
                  <div className="bg-neutral-50 rounded-md p-3 sm:p-4 border border-neutral-200">
                    <dt className="text-sm font-semibold leading-6 text-neutral-900 mb-2 sm:mb-3 flex items-center gap-2">
                      <div className="w-1.5 h-1.5 bg-neutral-600 rounded-full"></div>
                      Paradas
                    </dt>
                    <dd className="text-sm leading-6 text-neutral-700 bg-white rounded-md p-2 sm:p-3 border border-neutral-200">
                      {company.stations}
                    </dd>
                  </div>
                )}
                {company.directTransport &&
                  company.directTransport?.length > 0 && (
                    <div className="bg-success-50 rounded-md p-3 sm:p-4 border border-success-200">
                      <dt className="text-sm font-semibold leading-6 text-success-800 mb-2 sm:mb-3 flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-success-600 rounded-full"></div>
                        Colectivos Directos
                      </dt>
                      <dd className="text-sm leading-6 text-neutral-700">
                        <ListScheduleHours company={company.directTransport} />
                      </dd>
                    </div>
                  )}
                <AdditionalInformation dataImages={company.urlImages || []} />
              </dl>
            </div>
          </div>
        );
      })}
    </div>
  );
}
