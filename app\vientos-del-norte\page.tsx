import React from "react";
import { mockBusSchedules } from "@/mocks/busSchedules";
import CardScheduleBus from "@/components/CardScheduleBus";
import { Metadata } from "next";
import TitlePages from "@/components/TitlePages";

export const metadata: Metadata = {
  title: "Vientos del Norte Horarios de - Jujuy",
  description:
    "Horarios del colectivo Vientos del Norte, viajes a Barcena, Volcan, Purmamarca, Tilcara. Consulta los horarios de los colectivos al norte de Jujuy",
  keywords: [
    "Vientos del Norte",
    "colectivos al norte",
    "colectivos a Tilcara",
    "colectivos a Humahuaca",
    "colectivos a Purmamarca",
    "horario colectivos",
  ],
  robots: {
    index: true,
    follow: true,
  },
  formatDetection: {
    email: true,
    address: true,
    telephone: true,
  },
};

export default function page() {
  const vientosDelNorteFilter = mockBusSchedules.filter(
    (bus) => bus.nameCompany === "Vientos del Norte",
  );

  return (
    <div>
      <TitlePages title={"Horarios de Vientos del Norte"} />
      <CardScheduleBus mockBusSchedules={vientosDelNorteFilter} />
    </div>
  );
}
