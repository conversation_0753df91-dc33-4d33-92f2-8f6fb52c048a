import React from "react";

interface ITitlePagesProps {
  title: string;
}

export default function TitlePages({ title }: ITitlePagesProps) {
  return (
    <div className="px-4 sm:px-6 py-6 mb-6 bg-gradient-to-r from-primary-50 to-primary-100 border-l-4 border-primary-700 rounded-r-lg shadow-corporate">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-primary-700 mb-2 leading-tight">
          {title}
        </h1>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-primary-700 rounded-full"></div>
          <h3 className="text-sm sm:text-base text-primary-600 font-medium">
            Salida desde la Nueva Terminal
          </h3>
        </div>
      </div>
    </div>
  );
}
