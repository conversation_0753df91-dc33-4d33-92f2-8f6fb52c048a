import React from "react";

interface LogoAppProps {
  className?: string;
}

export default function LogoApp({ className = "w-10 h-10" }: LogoAppProps) {
  return (
    <div className={`${className} relative`}>
      <svg
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="w-full h-full"
      >
        {/* Fondo circular con gradiente */}
        <defs>
          <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#f0f4ff" />
            <stop offset="50%" stopColor="#e0e9ff" />
            <stop offset="100%" stopColor="#c7d6ff" />
          </linearGradient>
          <linearGradient id="busGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#06367c" />
            <stop offset="50%" stopColor="#052a63" />
            <stop offset="100%" stopColor="#041f4a" />
          </linearGradient>
          <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#8191ff" />
            <stop offset="100%" stopColor="#5d6aff" />
          </linearGradient>
        </defs>

        {/* Círculo de fondo */}
        <circle
          cx="50"
          cy="50"
          r="48"
          fill="url(#bgGradient)"
          stroke="#06367c"
          strokeWidth="2"
        />

        {/* Montañas del norte (representando el destino) */}
        <path
          d="M15 35 L25 25 L35 30 L45 20 L55 25 L65 18 L75 22 L85 15 L85 40 L15 40 Z"
          fill="url(#mountainGradient)"
          opacity="0.8"
        />

        {/* Bus moderno */}
        <g transform="translate(20, 45)">
          {/* Cuerpo del bus */}
          <rect
            x="0"
            y="0"
            width="45"
            height="20"
            rx="3"
            fill="url(#busGradient)"
          />
          
          {/* Ventanas */}
          <rect x="3" y="3" width="8" height="6" rx="1" fill="white" opacity="0.9" />
          <rect x="13" y="3" width="8" height="6" rx="1" fill="white" opacity="0.9" />
          <rect x="23" y="3" width="8" height="6" rx="1" fill="white" opacity="0.9" />
          <rect x="33" y="3" width="8" height="6" rx="1" fill="white" opacity="0.9" />
          
          {/* Puerta */}
          <rect x="3" y="11" width="6" height="8" rx="1" fill="white" opacity="0.7" />
          
          {/* Ruedas */}
          <circle cx="8" cy="25" r="4" fill="#2d3748" />
          <circle cx="8" cy="25" r="2" fill="#4a5568" />
          <circle cx="37" cy="25" r="4" fill="#2d3748" />
          <circle cx="37" cy="25" r="2" fill="#4a5568" />
          
          {/* Detalles del bus */}
          <rect x="0" y="10" width="45" height="1" fill="white" opacity="0.3" />
          <rect x="42" y="5" width="2" height="4" rx="1" fill="white" opacity="0.6" />
        </g>

        {/* Flecha direccional hacia el norte */}
        <g transform="translate(75, 55)">
          <path
            d="M0 8 L4 4 L8 8 M4 4 L4 12"
            stroke="#06367c"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            fill="none"
          />
        </g>

        {/* Texto "N" para Norte */}
        <text
          x="82"
          y="48"
          fontSize="8"
          fontWeight="bold"
          fill="#06367c"
          textAnchor="middle"
          fontFamily="Inter, sans-serif"
        >
          N
        </text>

        {/* Líneas de movimiento */}
        <g opacity="0.4">
          <path
            d="M10 60 Q20 58 30 60"
            stroke="#06367c"
            strokeWidth="1"
            fill="none"
            strokeDasharray="2,2"
          />
          <path
            d="M10 65 Q25 63 40 65"
            stroke="#06367c"
            strokeWidth="1"
            fill="none"
            strokeDasharray="2,2"
          />
        </g>
      </svg>
    </div>
  );
}
