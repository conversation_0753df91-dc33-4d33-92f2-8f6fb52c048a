import React from "react";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { FunnelIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

const navigation = [
  { name: "Todos", href: "/", current: false },
  { name: "<PERSON><PERSON>", href: "/evelia", current: false },
  { name: "Santa Ana", href: "/santa-ana", current: false },
  { name: "Panamericano", href: "/panamericano", current: false },
  { name: "El Quiaqueño", href: "/el-quiaquenio", current: false },
  { name: "Vientos del Norte", href: "/vientos-del-norte", current: false },
  { name: "Jamabus", href: "/jamabus", current: false },
  { name: "Balut", href: "/balut", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function LinkCompanyPages() {
  return (
    <div className="w-full">
      <Disclosure as="nav" className="bg-gradient-to-r from-neutral-50 to-neutral-100 border-b border-neutral-200/50 shadow-soft">
        <div className="mx-auto max-w-7xl">
          <div className="relative flex items-center sm:justify-between">
            <div className="flex sm:w-full items-center justify-between sm:items-stretch">
              <div className="hidden w-full sm:block">
                <div className="flex w-full justify-between gap-1 p-2">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      aria-current={item.current ? "page" : undefined}
                      className={classNames(
                        item.current
                          ? "bg-primary-500 text-white shadow-medium"
                          : "text-neutral-700 hover:bg-primary-50 hover:text-primary-700 border border-transparent hover:border-primary-200",
                        "px-4 py-2.5 w-full text-center text-sm font-medium rounded-xl transition-all duration-300 backdrop-blur-sm",
                      )}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <div className="inset-y-0 flex items-center sm:hidden p-4">
              {/* Mobile menu button*/}
              <DisclosureButton className="group relative inline-flex items-center justify-center rounded-xl p-2 text-neutral-600 hover:bg-primary-50 hover:text-primary-700 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 transition-all duration-300 mr-2">
                <span className="absolute -inset-0.5" />
                <span className="sr-only">Open main menu</span>
                <FunnelIcon
                  aria-hidden="true"
                  className="block h-5 w-5 group-data-[open]:hidden"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-5 w-5 group-data-[open]:block"
                />
              </DisclosureButton>
              <p className="sm:hidden text-sm text-neutral-700 font-medium">
                Filtrar por empresa
              </p>
            </div>
          </div>
        </div>

        <DisclosurePanel className="sm:hidden">
          <div className="space-y-2 px-4 pb-4 pt-2 bg-gradient-to-b from-neutral-50 to-neutral-100">
            {navigation.map((item) => (
              <DisclosureButton
                key={item.name}
                as="a"
                href={item.href}
                aria-current={item.current ? "page" : undefined}
                className={classNames(
                  item.current
                    ? "bg-primary-500 text-white shadow-medium"
                    : "text-neutral-700 hover:bg-primary-50 hover:text-primary-700 border border-neutral-200 hover:border-primary-200",
                  "block rounded-xl px-4 py-3 text-base font-medium transition-all duration-300",
                )}
              >
                {item.name}
              </DisclosureButton>
            ))}
          </div>
        </DisclosurePanel>
      </Disclosure>
    </div>
  );
}
