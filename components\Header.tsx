import React from "react";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { XMarkIcon, FunnelIcon } from "@heroicons/react/24/outline";
import Link from "next/link";
import Image from "next/image";
import ColectivoBlanco from "../assets/logo/colectivo_blanco.png";

const companyNavigation = [
  { name: "Todos", href: "/", current: false },
  { name: "<PERSON><PERSON>", href: "/evelia", current: false },
  { name: "Santa Ana", href: "/santa-ana", current: false },
  { name: "Panamericano", href: "/panamericano", current: false },
  { name: "El Quiaqueño", href: "/el-quiaquenio", current: false },
  { name: "Vientos del Norte", href: "/vientos-del-norte", current: false },
  { name: "Jamab<PERSON>", href: "/jamabus", current: false },
  { name: "<PERSON><PERSON>", href: "/balut", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}



export default function Header() {
  return (
    <header className="w-full fixed top-0 z-50">
      <Disclosure as="nav" className="bg-primary-700 shadow-2xl md:pt-2 md:pb-2 border-b border-primary-600">
        <div className="mx-auto max-w-7xl px-6 py-1 md:px-6 md:py-0 lg:px-8">
          <div className="relative flex h-18 md:h-18 items-center justify-between">
            {/* Logo - siempre visible */}
            <div className="flex flex-shrink-0 items-center">
              <Link href={"/"} className="flex items-center group p-1 md:p-0 rounded-lg transition-all duration-300 hover:bg-white/10">
                <Image
                  src={ColectivoBlanco}
                  alt="Colectivos Norte Jujuy"
                  className="h-9 w-9 md:h-8 md:w-8 group-hover:scale-105 transition-all duration-300 drop-shadow-lg"
                  width={36}
                  height={36}
                  priority
                />
                <div className="flex flex-col ml-3">
                  <p className="hidden md:block text-lg font-bold text-white tracking-wide">
                    Colectivos al Norte
                  </p>
                  <div className="hidden md:block w-full h-0.5 bg-gradient-to-r from-white/80 via-white/40 to-transparent mt-1"></div>
                </div>
              </Link>
            </div>

            {/* Título centrado en móvil - Mejorado */}
            <div className="flex flex-col items-center md:hidden px-4">
              <p className="font-bold text-white text-lg tracking-wide">
                Colectivos al Norte
              </p>
              <div className="w-24 h-0.5 bg-gradient-to-r from-white/80 via-white/60 to-white/80 mt-1.5 rounded-full"></div>
            </div>

            {/* Información de origen */}
            <div className="hidden md:flex md:ml-6 items-center">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-white/60 rounded-full"></div>
                <p className="text-white/90 text-sm font-medium tracking-wide">
                  Desde San Salvador de Jujuy
                </p>
              </div>
            </div>

            {/* Botón menú móvil - Diseño mejorado */}
            <div className="flex items-center md:hidden">
              <DisclosureButton className="group relative inline-flex items-center justify-center rounded-xl p-3 text-white hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-white/50 transition-all duration-300 border border-white/30 backdrop-blur-md shadow-xl hover:shadow-2xl hover:scale-105 active:scale-95">
                <span className="absolute -inset-1 rounded-xl bg-gradient-to-r from-white/5 to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                <span className="sr-only">Abrir menú principal</span>

                {/* Icono hamburger con animación personalizada */}
                <div className="relative w-6 h-6 flex flex-col justify-center items-center group-data-[open]:hidden">
                  <div className="w-5 h-0.5 bg-white rounded-full transform transition-all duration-300 group-hover:w-6 group-hover:bg-white/90"></div>
                  <div className="w-4 h-0.5 bg-white/80 rounded-full mt-1 transform transition-all duration-300 group-hover:w-6 group-hover:bg-white/90"></div>
                  <div className="w-5 h-0.5 bg-white rounded-full mt-1 transform transition-all duration-300 group-hover:w-6 group-hover:bg-white/90"></div>
                </div>

                {/* Icono X con animación */}
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-6 w-6 group-data-[open]:block transform transition-all duration-300 group-hover:rotate-90"
                />
              </DisclosureButton>
            </div>
          </div>
        </div>

        <DisclosurePanel className="md:hidden">
          <div className="bg-primary-800 border-t border-primary-600">
            {/* Información de origen */}
            <div className="flex items-center justify-center gap-2 py-3 border-b border-primary-700">
              <div className="w-2 h-2 bg-white/60 rounded-full"></div>
              <p className="text-white/90 text-sm font-medium tracking-wide">
                Desde San Salvador de Jujuy
              </p>
            </div>

            {/* Filtros por empresa */}
            <div className="px-4 py-4">
              <div className="space-y-1">
                {companyNavigation.map((item) => (
                  <DisclosureButton
                    key={item.name}
                    as="a"
                    href={item.href}
                    aria-current={item.current ? "page" : undefined}
                    className={classNames(
                      item.current
                        ? "bg-white/20 text-white"
                        : "text-white/90 hover:bg-white/10 hover:text-white",
                      "block rounded-md px-4 py-2.5 text-sm font-medium transition-all duration-200",
                    )}
                  >
                    {item.name}
                  </DisclosureButton>
                ))}
              </div>
            </div>
          </div>
        </DisclosurePanel>
      </Disclosure>

      {/* Navegación horizontal de filtros - Solo Desktop */}
      <nav className="hidden md:block bg-primary-700 shadow-corporate">
        <div className="mx-auto max-w-7xl">
          <div className="flex w-full justify-between gap-px">
            {companyNavigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                aria-current={item.current ? "page" : undefined}
                className={classNames(
                  item.current
                    ? "bg-white/20 text-white shadow-md"
                    : "text-white/90 hover:bg-white/10 hover:text-white",
                  "px-3 py-2 w-full text-center text-sm font-medium rounded-md transition-all duration-200",
                )}
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>
      </nav>
    </header>
  );
}
