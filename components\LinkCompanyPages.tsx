import React from "react";
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { FunnelIcon, XMarkIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

const navigation = [
  { name: "Todos", href: "/", current: false },
  { name: "<PERSON><PERSON>", href: "/evelia", current: false },
  { name: "Santa Ana", href: "/santa-ana", current: false },
  { name: "Panamericano", href: "/panamericano", current: false },
  { name: "El Quiaqueño", href: "/el-quiaquenio", current: false },
  { name: "Vientos del Norte", href: "/vientos-del-norte", current: false },
  { name: "Jamabus", href: "/jamabus", current: false },
  { name: "Balut", href: "/balut", current: false },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

export default function LinkCompanyPages() {
  return (
    <div className="w-full">
      <Disclosure as="nav" className="bg-primary-700 shadow-corporate">
        <div className="mx-auto max-w-7xl">
          <div className="relative flex items-center md:justify-between">
            <div className="flex md:w-full items-center justify-between md:items-stretch">
              <div className="hidden w-full md:block">
                <div className="flex w-full justify-between gap-px">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      aria-current={item.current ? "page" : undefined}
                      className={classNames(
                        item.current
                          ? "bg-white/20 text-white shadow-md"
                          : "text-white/90 hover:bg-white/10 hover:text-white",
                        "px-3 py-2 w-full text-center text-sm font-medium rounded-md transition-all duration-200",
                      )}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
            <div className="inset-y-0 flex items-center md:hidden">
              {/* Mobile filter button - elegante y moderno */}
              <DisclosureButton className="group relative inline-flex items-center justify-center bg-white/15 rounded-lg px-4 py-2 text-white hover:bg-white/25 focus:outline-none focus:ring-2 focus:ring-white/40 transition-all duration-300 border border-white/30 shadow-lg hover:shadow-xl">
                <FunnelIcon
                  aria-hidden="true"
                  className="block h-4 w-4 group-data-[open]:hidden mr-2 text-white"
                />
                <XMarkIcon
                  aria-hidden="true"
                  className="hidden h-4 w-4 group-data-[open]:block mr-2 text-white"
                />
                <span className="text-sm font-semibold tracking-wide">
                  Filtros
                </span>
                <div className="ml-2 flex flex-col gap-0.5">
                  <div className="w-1 h-0.5 bg-white/80 rounded-full group-data-[open]:rotate-45 transition-transform duration-300"></div>
                  <div className="w-1 h-0.5 bg-white/80 rounded-full group-data-[open]:-rotate-45 transition-transform duration-300"></div>
                </div>
              </DisclosureButton>
            </div>
          </div>
        </div>

        <DisclosurePanel className="sm:hidden">
          <div className="bg-primary-700 shadow-lg">
            <div className="px-4 py-4">
              <h3 className="text-sm font-semibold text-white flex items-center gap-2 mb-3">
                <FunnelIcon className="h-4 w-4 text-white" />
                Filtrar por empresa
              </h3>
              <div className="space-y-1">
                {navigation.map((item) => (
                  <DisclosureButton
                    key={item.name}
                    as="a"
                    href={item.href}
                    aria-current={item.current ? "page" : undefined}
                    className={classNames(
                      item.current
                        ? "bg-white/20 text-white"
                        : "text-white/90 hover:bg-white/10 hover:text-white",
                      "block rounded-md px-4 py-2.5 text-sm font-medium transition-all duration-200",
                    )}
                  >
                    {item.name}
                  </DisclosureButton>
                ))}
              </div>
            </div>
          </div>
        </DisclosurePanel>
      </Disclosure>
    </div>
  );
}
